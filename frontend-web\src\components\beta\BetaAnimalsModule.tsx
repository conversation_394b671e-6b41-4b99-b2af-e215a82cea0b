import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  LinearProgress,
  CircularProgress,
  useTheme,
  alpha,
  Divider,
  Tabs,
  Tab,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Avatar,
  IconButton,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper
} from '@mui/material';
import {
  Pets,
  Add,
  Lock,
  Star,
  TrendingUp,
  LocalHospital,
  MonetizationOn,
  Upgrade,
  Warning,
  Edit,
  Delete,
  AutoAwesome,
  Category
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, Legend } from 'recharts';
import { useNavigate } from 'react-router-dom';
import { useMongoAnimalData } from '../../hooks/useMongoAnimalData';
import { useLanguage } from '../../contexts/LanguageContext';
import { AnimatedBackgroundCard, ModuleContentCard } from '../common';
import AnimalCard from '../animals/AnimalCard';

// TabPanel component for Material-UI Tabs
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const BETA_ANIMAL_LIMIT = 50;

interface BetaAnimalsModuleProps {
  userTier: 'beta' | 'professional' | 'enterprise';
}

const BetaAnimalsModule: React.FC<BetaAnimalsModuleProps> = ({ userTier }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { translate } = useLanguage();
  const { animals, stats, createAnimal, loading } = useMongoAnimalData();

  const [showUpgradeDialog, setShowUpgradeDialog] = useState(false);
  const [localLoading, setLocalLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [newAnimal, setNewAnimal] = useState<any>({});

  // Calculate current usage
  const currentAnimalCount = animals?.length || 0;
  const usagePercentage = (currentAnimalCount / BETA_ANIMAL_LIMIT) * 100;
  const isAtLimit = currentAnimalCount >= BETA_ANIMAL_LIMIT;

  // Category data for dashboard
  const categoryData = [
    { name: 'Cattle', value: stats?.bySpecies?.cattle || 15, color: '#2E7D32', icon: '🐄' },
    { name: 'Sheep', value: stats?.bySpecies?.sheep || 12, color: '#1976D2', icon: '🐑' },
    { name: 'Goats', value: stats?.bySpecies?.goats || 8, color: '#F57C00', icon: '🐐' },
    { name: 'Pigs', value: stats?.bySpecies?.pigs || 5, color: '#7B1FA2', icon: '🐷' },
    { name: 'Chickens', value: stats?.bySpecies?.chickens || 10, color: '#D32F2F', icon: '🐔' }
  ];

  // Health stats from animal data
  const healthStats = stats?.byHealth || {
    healthy: 0,
    sick: 0,
    injured: 0,
    pregnant: 0
  };

  // Species stats from animal data
  const speciesStats = stats?.bySpecies || {
    cattle: 0,
    sheep: 0,
    goats: 0,
    pigs: 0,
    chickens: 0
  };

  // Status stats from animal data
  const statusStats = stats?.byStatus || {
    active: animals?.length || 0,
    inactive: 0,
    sold: 0,
    deceased: 0
  };

  // Filter animals by category
  const filteredAnimals = selectedCategory === 'all'
    ? animals
    : animals?.filter(animal => animal.species?.toLowerCase() === selectedCategory.toLowerCase());

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleAddAnimal = async () => {
    if (isAtLimit) {
      setShowUpgradeDialog(true);
      return;
    }

    if (showAddDialog) {
      // This is the actual add operation
      try {
        setLocalLoading(true);

        // Validate required fields
        if (!newAnimal.name || !newAnimal.species || !newAnimal.tagNumber) {
          alert('Please fill in all required fields (Name, Species, Tag Number)');
          return;
        }

        // Create animal data
        const animalData = {
          ...newAnimal,
          id: `beta_animal_${Date.now()}`,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'beta_user'
        };

        // Add to animals list
        setAnimals(prev => [...prev, animalData]);

        // Reset form and close dialog
        setNewAnimal({});
        setShowAddDialog(false);

        console.log('Animal added successfully:', animalData);
      } catch (error) {
        console.error('Error adding animal:', error);
        alert('Error adding animal. Please try again.');
      } finally {
        setLocalLoading(false);
      }
    } else {
      setShowAddDialog(true);
    }
  };

  const handleUpgrade = (tier: 'professional' | 'enterprise') => {
    navigate(`/subscription?tier=${tier}`);
  };

  const handleCategoryClick = (category: string) => {
    setSelectedCategory(category);
  };

  const handleEditAnimal = (animal: any) => {
    setNewAnimal(animal);
    setShowEditDialog(true);
  };

  const handleDeleteAnimal = async (animalId: string) => {
    if (window.confirm('Are you sure you want to delete this animal?')) {
      try {
        setLoading(true);

        // Remove from animals list
        setAnimals(prev => prev.filter(animal => animal.id !== animalId));

        console.log('Animal deleted successfully:', animalId);
      } catch (error) {
        console.error('Error deleting animal:', error);
        alert('Error deleting animal. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleUpdateAnimal = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (!newAnimal.name || !newAnimal.species || !newAnimal.tagNumber) {
        alert('Please fill in all required fields (Name, Species, Tag Number)');
        return;
      }

      // Update animal in list
      setAnimals(prev => prev.map(animal =>
        animal.id === newAnimal.id
          ? { ...newAnimal, updatedAt: new Date() }
          : animal
      ));

      setShowEditDialog(false);
      setNewAnimal({});

      console.log('Animal updated successfully:', newAnimal);
    } catch (error) {
      console.error('Error updating animal:', error);
      alert('Error updating animal. Please try again.');
    } finally {
      setLoading(false);
    }
  };



  // Show loading state
  if (loading) {
    return (
      <Box sx={{ p: 3, display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading BETA Animals Data...
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* BETA Header with Usage Indicator */}
      <Box sx={{ mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4" fontWeight="bold" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Pets sx={{ color: theme.palette.primary.main }} />
            Animals Module
            <Chip
              label="BETA"
              size="small"
              sx={{
                backgroundColor: alpha(theme.palette.warning.main, 0.2),
                color: theme.palette.warning.main,
                fontWeight: 'bold'
              }}
            />
          </Typography>

          <Button
            variant="contained"
            startIcon={isAtLimit ? <Lock /> : <Add />}
            onClick={handleAddAnimal}
            disabled={loading}
            sx={{
              backgroundColor: isAtLimit ? theme.palette.warning.main : theme.palette.primary.main,
              '&:hover': {
                backgroundColor: isAtLimit ? theme.palette.warning.dark : theme.palette.primary.dark,
              }
            }}
          >
            {isAtLimit ? 'Upgrade to Add More' : 'Add Animal'}
          </Button>
        </Box>

        {/* Usage Progress Bar */}
        <Card sx={{ mb: 3, backgroundColor: alpha(theme.palette.primary.main, 0.05) }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                BETA Usage: {currentAnimalCount} / {BETA_ANIMAL_LIMIT} Animals
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {usagePercentage.toFixed(1)}% Used
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={usagePercentage}
              sx={{
                height: 8,
                borderRadius: 4,
                backgroundColor: alpha(theme.palette.grey[300], 0.3),
                '& .MuiLinearProgress-bar': {
                  backgroundColor: usagePercentage > 80 ? theme.palette.warning.main : theme.palette.primary.main
                }
              }}
            />
            {usagePercentage > 80 && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  You're approaching the BETA limit! Upgrade to Professional (R299/month) for up to 500 animals.
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #4CAF50, #66BB6A)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {animals.length}/50
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Total Animals (BETA Limit)
                    </Typography>
                  </Box>
                  <Pets sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #2196F3, #42A5F5)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {healthStats.healthy || 0}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Healthy Animals
                    </Typography>
                  </Box>
                  <TrendingUp sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #FF9800, #FFB74D)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {Object.keys(speciesStats).length}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Species Types
                    </Typography>
                  </Box>
                  <Category sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>

        <Grid item xs={12} md={3}>
          <motion.div whileHover={{ y: -4 }}>
            <Card sx={{ borderRadius: 3, background: 'linear-gradient(135deg, #9C27B0, #BA68C8)' }}>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                  <Box>
                    <Typography variant="h4" color="white" fontWeight="bold">
                      {statusStats.active || 0}
                    </Typography>
                    <Typography variant="body2" color="rgba(255,255,255,0.8)">
                      Active Animals
                    </Typography>
                  </Box>
                  <Pets sx={{ fontSize: 40, color: 'rgba(255,255,255,0.8)' }} />
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card sx={{ borderRadius: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
            <Tab label="Animals List" />
            <Tab label="Category Dashboard" />
            <Tab label="AI Features (Premium)" disabled />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          {/* Animals List */}
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Tag Number</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Species</TableCell>
                  <TableCell>Breed</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Health</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {animals.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                      <Typography variant="h6" color="text.secondary">
                        No animals found
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        Click "Add Animal" to create your first livestock record
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : animals.map((animal) => (
                  <TableRow key={animal.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {animal.tagNumber}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Avatar sx={{ width: 32, height: 32, bgcolor: 'primary.main' }}>
                          {animal.name.charAt(0)}
                        </Avatar>
                        {animal.name}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip label={animal.species} size="small" color="primary" />
                    </TableCell>
                    <TableCell>{animal.breed}</TableCell>
                    <TableCell>
                      <Chip
                        label={animal.status}
                        size="small"
                        color={animal.status === 'active' ? 'success' : 'default'}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={animal.healthStatus}
                        size="small"
                        color={animal.healthStatus === 'healthy' ? 'success' : 'warning'}
                      />
                    </TableCell>
                    <TableCell>
                      <Box display="flex" gap={1}>
                        <IconButton size="small" onClick={() => handleEditAnimal(animal)}>
                          <Edit />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDeleteAnimal(animal.id)}>
                          <Delete />
                        </IconButton>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {/* Category Dashboard */}
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" mb={2}>
                    Species Distribution
                  </Typography>
                  {Object.entries(speciesStats).map(([species, count]) => (
                    <Box key={species} mb={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2">{species}</Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {count} animals
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          height: 8,
                          backgroundColor: '#f0f0f0',
                          borderRadius: 4,
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${(count / animals.length) * 100}%`,
                            backgroundColor: '#4CAF50',
                            borderRadius: 4,
                          }}
                        />
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" fontWeight="bold" mb={2}>
                    Health Status
                  </Typography>
                  {Object.entries(healthStats).map(([status, count]) => (
                    <Box key={status} mb={2}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                          {status}
                        </Typography>
                        <Typography variant="body2" fontWeight="bold">
                          {count} animals
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          height: 8,
                          backgroundColor: '#f0f0f0',
                          borderRadius: 4,
                          overflow: 'hidden'
                        }}
                      >
                        <Box
                          sx={{
                            height: '100%',
                            width: `${(count / animals.length) * 100}%`,
                            backgroundColor: status === 'healthy' ? '#4CAF50' : '#FF9800',
                            borderRadius: 4,
                          }}
                        />
                      </Box>
                    </Box>
                  ))}
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ borderRadius: 2, height: '100%' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
                    <Typography variant="h6" fontWeight="bold">
                      AI Auto-Categorization
                    </Typography>
                    <AutoAwesome sx={{ color: '#FF9800' }} />
                  </Box>
                  <Typography variant="body2" color="text.secondary" mb={2}>
                    Upgrade to Professional for:
                  </Typography>
                  <Box component="ul" sx={{ pl: 2, mb: 2 }}>
                    <Typography component="li" variant="body2" mb={1}>
                      Automatic breed identification
                    </Typography>
                    <Typography component="li" variant="body2" mb={1}>
                      Smart health predictions
                    </Typography>
                    <Typography component="li" variant="body2" mb={1}>
                      Performance categorization
                    </Typography>
                    <Typography component="li" variant="body2" mb={1}>
                      Breeding recommendations
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<Upgrade />}
                    sx={{
                      background: 'linear-gradient(135deg, #FF9800, #FFB74D)',
                      borderRadius: 2,
                    }}
                  >
                    Upgrade Now
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>

      {/* Add Animal Dialog */}
      <Dialog open={showAddDialog} onClose={() => setShowAddDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Add New Animal</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tag Number"
                value={newAnimal.tagNumber || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, tagNumber: e.target.value })}
                placeholder="e.g., C001, S001"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={newAnimal.name || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, name: e.target.value })}
                placeholder="e.g., Bessie, Daisy"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Species</InputLabel>
                <Select
                  value={newAnimal.species || ''}
                  onChange={(e) => setNewAnimal({ ...newAnimal, species: e.target.value })}
                  label="Species"
                >
                  <MenuItem value="Cattle">Cattle</MenuItem>
                  <MenuItem value="Sheep">Sheep</MenuItem>
                  <MenuItem value="Goats">Goats</MenuItem>
                  <MenuItem value="Pigs">Pigs</MenuItem>
                  <MenuItem value="Chickens">Chickens</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Breed"
                value={newAnimal.breed || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, breed: e.target.value })}
                placeholder="e.g., Holstein, Jersey, Merino"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Gender</InputLabel>
                <Select
                  value={newAnimal.gender || 'female'}
                  onChange={(e) => setNewAnimal({ ...newAnimal, gender: e.target.value as 'male' | 'female' })}
                  label="Gender"
                >
                  <MenuItem value="male">Male</MenuItem>
                  <MenuItem value="female">Female</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Birth Date"
                type="date"
                value={newAnimal.birthDate || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, birthDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Weight (kg)"
                type="number"
                value={newAnimal.weight || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, weight: Number(e.target.value) })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Location"
                value={newAnimal.location || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, location: e.target.value })}
                placeholder="e.g., Pasture A, Paddock 1"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes (Optional)"
                value={newAnimal.notes || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, notes: e.target.value })}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleAddAnimal}>
            Add Animal
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Animal Dialog */}
      <Dialog open={showEditDialog} onClose={() => setShowEditDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Animal</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tag Number"
                value={newAnimal.tagNumber || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, tagNumber: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Name"
                value={newAnimal.name || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Species</InputLabel>
                <Select
                  value={newAnimal.species || ''}
                  onChange={(e) => setNewAnimal({ ...newAnimal, species: e.target.value })}
                  label="Species"
                >
                  <MenuItem value="Cattle">Cattle</MenuItem>
                  <MenuItem value="Sheep">Sheep</MenuItem>
                  <MenuItem value="Goats">Goats</MenuItem>
                  <MenuItem value="Pigs">Pigs</MenuItem>
                  <MenuItem value="Chickens">Chickens</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Breed"
                value={newAnimal.breed || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, breed: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={newAnimal.status || 'active'}
                  onChange={(e) => setNewAnimal({ ...newAnimal, status: e.target.value as any })}
                  label="Status"
                >
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="sold">Sold</MenuItem>
                  <MenuItem value="deceased">Deceased</MenuItem>
                  <MenuItem value="quarantined">Quarantined</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Health Status</InputLabel>
                <Select
                  value={newAnimal.healthStatus || 'healthy'}
                  onChange={(e) => setNewAnimal({ ...newAnimal, healthStatus: e.target.value as any })}
                  label="Health Status"
                >
                  <MenuItem value="healthy">Healthy</MenuItem>
                  <MenuItem value="sick">Sick</MenuItem>
                  <MenuItem value="injured">Injured</MenuItem>
                  <MenuItem value="recovering">Recovering</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                value={newAnimal.notes || ''}
                onChange={(e) => setNewAnimal({ ...newAnimal, notes: e.target.value })}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowEditDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={handleUpdateAnimal}>
            Update Animal
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BetaAnimalsModule;
