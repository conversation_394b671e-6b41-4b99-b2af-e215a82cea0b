import React, { useState, useEffect } from 'react';
import { Box, Typography, IconButton, Paper, Divider, Grid, Card, CardContent, Tabs, Tab, alpha, useTheme } from '@mui/material';
import {
  Close,
  WbSunny,
  Opacity,
  AcUnit,
  Waves,
  Air,
  WaterDrop,
  Visibility,
  Speed,
  LocationOn,
  Warning
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useLanguage } from '../contexts/LanguageContext';
import { GlassCard } from '../design/DesignSystem';

interface WeatherData {
  location: string;
  currentWeather: {
    temperature: number;
    feelsLike: number;
    humidity: number;
    windSpeed: number;
    windDirection: string;
    pressure: number;
    visibility: number;
    uvIndex: number;
    condition: string;
    icon: string;
  };
  forecast: Array<{
    date: string;
    dayOfWeek: string;
    maxTemp: number;
    minTemp: number;
    condition: string;
    icon: string;
    precipitation: number;
    humidity: number;
    windSpeed: number;
  }>;
  alerts: Array<{
    title: string;
    description: string;
    severity: 'moderate' | 'severe' | 'extreme';
    validUntil: string;
  }>;
}

interface WeatherWidgetProps {
  onClose: () => void;
}

const WeatherWidget: React.FC<WeatherWidgetProps> = ({ onClose }) => {
  const [weatherData, setWeatherData] = useState<WeatherData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const [error, setError] = useState<string | null>(null);
  
  const theme = useTheme();
  const { translate } = useLanguage();
  
  // Fetch weather data on mount
  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      try {
        // Mock weather data
        const mockWeatherData: WeatherData = {
          location: 'Johannesburg, South Africa',
          currentWeather: {
            temperature: 24,
            feelsLike: 26,
            humidity: 65,
            windSpeed: 12,
            windDirection: 'NE',
            pressure: 1012,
            visibility: 10,
            uvIndex: 6,
            condition: 'Partly Cloudy',
            icon: 'partly-cloudy'
          },
          forecast: [
            {
              date: '2023-05-15',
              dayOfWeek: 'Monday',
              maxTemp: 26,
              minTemp: 18,
              condition: 'Sunny',
              icon: 'sunny',
              precipitation: 0,
              humidity: 60,
              windSpeed: 10
            },
            {
              date: '2023-05-16',
              dayOfWeek: 'Tuesday',
              maxTemp: 28,
              minTemp: 19,
              condition: 'Partly Cloudy',
              icon: 'partly-cloudy',
              precipitation: 10,
              humidity: 65,
              windSpeed: 12
            },
            {
              date: '2023-05-17',
              dayOfWeek: 'Wednesday',
              maxTemp: 25,
              minTemp: 17,
              condition: 'Scattered Showers',
              icon: 'rain',
              precipitation: 40,
              humidity: 75,
              windSpeed: 15
            },
            {
              date: '2023-05-18',
              dayOfWeek: 'Thursday',
              maxTemp: 22,
              minTemp: 16,
              condition: 'Rain',
              icon: 'rain',
              precipitation: 80,
              humidity: 85,
              windSpeed: 18
            },
            {
              date: '2023-05-19',
              dayOfWeek: 'Friday',
              maxTemp: 20,
              minTemp: 14,
              condition: 'Cloudy',
              icon: 'cloudy',
              precipitation: 30,
              humidity: 70,
              windSpeed: 14
            }
          ],
          alerts: [
            {
              title: 'Heavy Rain Warning',
              description: 'Heavy rainfall expected on Wednesday and Thursday. Potential for localized flooding in low-lying areas.',
              severity: 'moderate',
              validUntil: '2023-05-18T23:59:59Z'
            }
          ]
        };
        
        setWeatherData(mockWeatherData);
        setIsLoading(false);
      } catch (err) {
        setError('Failed to load weather data');
        setIsLoading(false);
      }
    }, 1500);
    
    // Add event listener for Escape key
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };
    
    window.addEventListener('keydown', handleEscKey);
    
    return () => {
      window.removeEventListener('keydown', handleEscKey);
    };
  }, [onClose]);
  
  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };
  
  // Get weather icon
  const getWeatherIcon = (iconCode: string) => {
    switch (iconCode) {
      case 'sunny':
        return <WbSunny sx={{ color: '#FFD700' }} />;
      case 'partly-cloudy':
        return <WbSunny sx={{ color: '#FFD700' }} />;
      case 'cloudy':
        return <Waves sx={{ color: '#A9A9A9' }} />;
      case 'rain':
        return <Opacity sx={{ color: '#4682B4' }} />;
      case 'snow':
        return <AcUnit sx={{ color: '#E0FFFF' }} />;
      default:
        return <WbSunny sx={{ color: '#FFD700' }} />;
    }
  };
  
  // Get alert severity color
  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'moderate':
        return theme.palette.warning.main;
      case 'severe':
        return theme.palette.error.main;
      case 'extreme':
        return theme.palette.error.dark;
      default:
        return theme.palette.warning.main;
    }
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric' 
    });
  };
  
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.2 }}
      style={{
        position: 'fixed',
        top: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        maxWidth: '400px',
        zIndex: theme.zIndex.drawer + 2,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Paper
        elevation={4}
        sx={{
          height: '100%',
          borderRadius: 0,
          display: 'flex',
          flexDirection: 'column',
          bgcolor: alpha(theme.palette.background.paper, 0.95),
          backdropFilter: 'blur(10px)',
          borderLeft: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <WbSunny color="primary" sx={{ mr: 1 }} />
            <Typography variant="h6" fontWeight="bold">
              {translate('common.weather')}
            </Typography>
          </Box>
          
          <IconButton edge="end" onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
        
        {/* Tabs */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={activeTab}
            onChange={handleTabChange}
            variant="fullWidth"
            indicatorColor="primary"
            textColor="primary"
          >
            <Tab label={translate('common.today')} />
            <Tab label={translate('common.forecast')} />
            <Tab label={translate('common.alerts')} />
          </Tabs>
        </Box>
        
        {/* Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
          {isLoading ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                {translate('common.loading')}...
              </Typography>
            </Box>
          ) : error ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" color="error">
                {error}
              </Typography>
            </Box>
          ) : weatherData ? (
            <>
              {/* Today's Weather */}
              {activeTab === 0 && (
                <Box>
                  {/* Location */}
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationOn color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6">
                      {weatherData.location}
                    </Typography>
                  </Box>
                  
                  {/* Current Weather */}
                  <GlassCard sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box>
                        <Typography variant="h2" fontWeight="bold">
                          {weatherData.currentWeather.temperature}°C
                        </Typography>
                        <Typography variant="body1">
                          {weatherData.currentWeather.condition}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {translate('common.feels_like')} {weatherData.currentWeather.feelsLike}°C
                        </Typography>
                      </Box>
                      
                      <Box sx={{ textAlign: 'center' }}>
                        <Box sx={{ fontSize: 64 }}>
                          {getWeatherIcon(weatherData.currentWeather.icon)}
                        </Box>
                      </Box>
                    </Box>
                  </GlassCard>
                  
                  {/* Weather Details */}
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mb: 2 }}>
                    {translate('common.weather_details')}
                  </Typography>
                  
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <WaterDrop sx={{ mr: 1, color: theme.palette.info.main }} />
                            <Typography variant="body2" color="text.secondary">
                              {translate('common.humidity')}
                            </Typography>
                          </Box>
                          <Typography variant="h6">
                            {weatherData.currentWeather.humidity}%
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Air sx={{ mr: 1, color: theme.palette.info.main }} />
                            <Typography variant="body2" color="text.secondary">
                              {translate('common.wind')}
                            </Typography>
                          </Box>
                          <Typography variant="h6">
                            {weatherData.currentWeather.windSpeed} km/h
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {weatherData.currentWeather.windDirection}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Speed sx={{ mr: 1, color: theme.palette.info.main }} />
                            <Typography variant="body2" color="text.secondary">
                              {translate('common.pressure')}
                            </Typography>
                          </Box>
                          <Typography variant="h6">
                            {weatherData.currentWeather.pressure} hPa
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                    
                    <Grid item xs={6}>
                      <Card sx={{ height: '100%' }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Visibility sx={{ mr: 1, color: theme.palette.info.main }} />
                            <Typography variant="body2" color="text.secondary">
                              {translate('common.visibility')}
                            </Typography>
                          </Box>
                          <Typography variant="h6">
                            {weatherData.currentWeather.visibility} km
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                  
                  {/* Farm Impact */}
                  <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 3, mb: 2 }}>
                    {translate('common.farm_impact')}
                  </Typography>
                  
                  <Card>
                    <CardContent>
                      <Typography variant="body2">
                        {translate('common.weather_farm_impact')}
                      </Typography>
                      
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" fontWeight="bold">
                          {translate('common.recommendations')}:
                        </Typography>
                        <ul className="weather-recommendations-list">
                          <li>
                            <Typography variant="body2">
                              {translate('common.weather_recommendation_1')}
                            </Typography>
                          </li>
                          <li>
                            <Typography variant="body2">
                              {translate('common.weather_recommendation_2')}
                            </Typography>
                          </li>
                        </ul>
                      </Box>
                    </CardContent>
                  </Card>
                </Box>
              )}
              
              {/* Forecast */}
              {activeTab === 1 && (
                <Box>
                  {weatherData.forecast.map((day, index) => (
                    <Card key={index} sx={{ mb: 2 }}>
                      <CardContent>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <Box>
                            <Typography variant="subtitle1" fontWeight="bold">
                              {day.dayOfWeek}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {formatDate(day.date)}
                            </Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            {getWeatherIcon(day.icon)}
                            <Box sx={{ ml: 2, textAlign: 'right' }}>
                              <Typography variant="body1" fontWeight="bold">
                                {day.maxTemp}° / {day.minTemp}°
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {day.condition}
                              </Typography>
                            </Box>
                          </Box>
                        </Box>
                        
                        <Divider sx={{ my: 2 }} />
                        
                        <Grid container spacing={2}>
                          <Grid item xs={4}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="body2" color="text.secondary">
                                {translate('common.precipitation')}
                              </Typography>
                              <Typography variant="body1">
                                {day.precipitation}%
                              </Typography>
                            </Box>
                          </Grid>
                          
                          <Grid item xs={4}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="body2" color="text.secondary">
                                {translate('common.humidity')}
                              </Typography>
                              <Typography variant="body1">
                                {day.humidity}%
                              </Typography>
                            </Box>
                          </Grid>
                          
                          <Grid item xs={4}>
                            <Box sx={{ textAlign: 'center' }}>
                              <Typography variant="body2" color="text.secondary">
                                {translate('common.wind')}
                              </Typography>
                              <Typography variant="body1">
                                {day.windSpeed} km/h
                              </Typography>
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>
                  ))}
                </Box>
              )}
              
              {/* Alerts */}
              {activeTab === 2 && (
                <Box>
                  {weatherData.alerts.length > 0 ? (
                    weatherData.alerts.map((alert, index) => (
                      <Card 
                        key={index} 
                        sx={{ 
                          mb: 2, 
                          borderLeft: `4px solid ${getAlertSeverityColor(alert.severity)}` 
                        }}
                      >
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                            <Warning sx={{ 
                              color: getAlertSeverityColor(alert.severity),
                              mr: 1,
                              mt: 0.5
                            }} />
                            
                            <Box>
                              <Typography variant="subtitle1" fontWeight="bold">
                                {alert.title}
                              </Typography>
                              
                              <Typography variant="body2" sx={{ mt: 1 }}>
                                {alert.description}
                              </Typography>
                              
                              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                                {translate('common.valid_until')}: {formatDate(alert.validUntil)}
                              </Typography>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                      <Typography variant="body1" color="text.secondary">
                        {translate('common.no_weather_alerts')}
                      </Typography>
                    </Box>
                  )}
                </Box>
              )}
            </>
          ) : null}
        </Box>
        
        {/* Footer */}
        <Box
          sx={{
            p: 2,
            borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            textAlign: 'center'
          }}
        >
          <Typography variant="caption" color="text.secondary">
            {translate('common.weather_data_source')}
          </Typography>
        </Box>
      </Paper>
    </motion.div>
  );
};

export default WeatherWidget;
