/**
 * AgriIntel Main Stylesheet
 * Comprehensive external CSS architecture with zero inline violations
 */

/* Import main CSS architecture */
@import url('./styles/main.css');

/* Import global background system */
@import url('./styles/global-background.css');

/* Import final theme consistency overrides */
@import url('./styles/final-theme-consistency.css');

/* Tailwind CSS (if needed for utilities) */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Legacy imports - will be migrated */
@import './styles/typography.css';
@import './styles/unified-sections.css';
@import './styles/background-optimization.css';
@import './styles/backgrounds.css';
@import './styles/animations.css';
@import './styles/responsive.css';
@import './styles/tabFixes.css';
@import './styles/buttonOverrides.css';

/* New modern SaaS landing page */
@import './styles/modern-saas-landing.css';
@import './styles/beta-v1-login.css';
@import './styles/professional-v1-login.css';
@import './styles/mui-button-fix.css';
@import './styles/reportsFixes.css';
@import './styles/reportsButtonFix.css';
@import './styles/mui-button-fix-reports.css';
@import './styles/resizeObserverFix.css';
@import './styles/direct-mui-button-fix.css';
@import './styles/final-mui-button-fix.css';
@import './styles/mui-button-global-fix.css';

:root {
  /* Primary color palette based on README */
  --color-primary-50: #e6f7f6;
  --color-primary-100: #c2ebe8;
  --color-primary-200: #9cdedb;
  --color-primary-300: #75d0cd;
  --color-primary-400: #4ec3bf;
  --color-primary-500: #27b5b0;
  --color-primary-600: #1f958f;
  --color-primary-700: #17756f;
  --color-primary-800: #0f766e; /* Primary teal color from README */
  --color-primary-900: #085752;

  /* Secondary color palette */
  --color-secondary-50: #f8f9fa;
  --color-secondary-100: #f1f3f5;
  --color-secondary-200: #e9ecef;
  --color-secondary-300: #dee2e6;
  --color-secondary-400: #ced4da;
  --color-secondary-500: #adb5bd;
  --color-secondary-600: #868e96;
  --color-secondary-700: #495057;
  --color-secondary-800: #374151; /* Secondary gray color from README */
  --color-secondary-900: #212529;

  /* Accent color palette */
  --color-accent-50: #f5f3ff;
  --color-accent-100: #ede9fe;
  --color-accent-200: #ddd6fe;
  --color-accent-300: #c4b5fd;
  --color-accent-400: #a78bfa;
  --color-accent-500: #8b5cf6;
  --color-accent-600: #7c3aed;
  --color-accent-700: #6d28d9;
  --color-accent-800: #5b21b6;
  --color-accent-900: #9333EA; /* Accent purple color from README */

  /* Status colors */
  --color-success: #059669; /* Success green from README */
  --color-warning: #D97706; /* Warning amber from README */
  --color-danger: #DC2626; /* Danger red from README */
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f9fafb;
  color: #374151;
}

code {
  font-family: 'JetBrains Mono', source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom component styles */
.card {
  @apply bg-white rounded-lg shadow-md p-6 transition-all duration-200;
}

.card:hover {
  @apply shadow-lg;
}

.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-secondary {
  @apply bg-secondary-200 hover:bg-secondary-300 text-secondary-800 font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.btn-accent {
  @apply bg-accent-600 hover:bg-accent-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200;
}

.input-field {
  @apply border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
}

.section-title {
  @apply text-2xl font-bold text-gray-900 mb-4;
}

.section-subtitle {
  @apply text-lg font-medium text-gray-700 mb-2;
}

/* Status badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Login page styles */
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f766e, #059669);
  padding: 20px;
}

.login-paper {
  padding: 40px !important;
  max-width: 400px;
  width: 100%;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.login-button {
  margin-top: 24px !important;
  height: 48px !important;
  font-size: 16px !important;
  font-weight: 600 !important;
}