import React from 'react';
import { Box, Typography } from '@mui/material';
import { Agriculture } from '@mui/icons-material';

interface AgriIntelLogoProps {
  variant?: 'full' | 'compact' | 'icon-only' | 'text-only';
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  theme?: 'beta' | 'professional' | 'white' | 'dark';
  showTagline?: boolean;
  orientation?: 'horizontal' | 'vertical';
  onClick?: () => void;
  className?: string;
}

const AgriIntelLogo: React.FC<AgriIntelLogoProps> = ({
  variant = 'full',
  size = 'medium',
  theme = 'professional',
  showTagline = false,
  orientation = 'horizontal',
  onClick,
  className = ''
}) => {
  // Size configurations
  const sizeConfig = {
    small: {
      iconSize: 24,
      fontSize: '1.2rem',
      taglineSize: '0.7rem',
      spacing: 8,
      height: 32
    },
    medium: {
      iconSize: 32,
      fontSize: '1.8rem',
      taglineSize: '0.8rem',
      spacing: 12,
      height: 48
    },
    large: {
      iconSize: 40,
      fontSize: '2.2rem',
      taglineSize: '0.9rem',
      spacing: 16,
      height: 56
    },
    'extra-large': {
      iconSize: 48,
      fontSize: '2.8rem',
      taglineSize: '1rem',
      spacing: 20,
      height: 72
    }
  };

  // Theme configurations
  const themeConfig = {
    beta: {
      primary: '#FF9800',
      secondary: '#FFA726',
      text: '#E65100',
      gradient: 'linear-gradient(135deg, #FF9800 0%, #FFA726 100%)'
    },
    professional: {
      primary: '#4CAF50',
      secondary: '#66BB6A',
      text: '#2E7D32',
      gradient: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)'
    },
    white: {
      primary: '#FFFFFF',
      secondary: '#F5F5F5',
      text: '#FFFFFF',
      gradient: 'linear-gradient(135deg, #FFFFFF 0%, #F5F5F5 100%)'
    },
    dark: {
      primary: '#1A1A1A',
      secondary: '#333333',
      text: '#1A1A1A',
      gradient: 'linear-gradient(135deg, #1A1A1A 0%, #333333 100%)'
    }
  };

  const config = sizeConfig[size];
  const colors = themeConfig[theme];

  // Logo image component
  const LogoImage = () => (
    <Box
      component="img"
      src="/images/logo/AgriIntel Logo with Bright Accents and Livestock.png"
      alt="AgriIntel Logo"
      sx={{
        height: config.height,
        width: 'auto',
        objectFit: 'contain',
        filter: theme === 'white' ? 'brightness(0) invert(1)' : 'none'
      }}
    />
  );

  // Icon component (fallback if image fails)
  const IconComponent = () => (
    <Agriculture
      sx={{
        fontSize: config.iconSize,
        color: colors.primary,
        filter: `drop-shadow(0 2px 4px rgba(0,0,0,0.2))`
      }}
    />
  );

  // Text component
  const TextComponent = () => (
    <Box>
      <Typography
        variant="h6"
        component="span"
        sx={{
          fontSize: config.fontSize,
          fontWeight: 700,
          background: colors.gradient,
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
          lineHeight: 1,
          fontFamily: '"Segoe UI", "Roboto", "Arial", sans-serif'
        }}
      >
        AgriIntel
      </Typography>
      {showTagline && (
        <Typography
          variant="caption"
          component="div"
          sx={{
            fontSize: config.taglineSize,
            color: colors.text,
            fontWeight: 500,
            letterSpacing: '0.5px',
            opacity: 0.8,
            lineHeight: 1.2,
            mt: 0.5
          }}
        >
          Smart Farming, Smart Decisions
        </Typography>
      )}
    </Box>
  );

  // Render based on variant
  const renderContent = () => {
    switch (variant) {
      case 'icon-only':
        return <LogoImage />;
      case 'text-only':
        return <TextComponent />;
      case 'compact':
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: config.spacing / 8,
              flexDirection: orientation === 'vertical' ? 'column' : 'row'
            }}
          >
            <IconComponent />
            <Typography
              variant="h6"
              component="span"
              sx={{
                fontSize: config.fontSize * 0.8,
                fontWeight: 700,
                color: colors.primary,
                lineHeight: 1
              }}
            >
              AgriIntel
            </Typography>
          </Box>
        );
      case 'full':
      default:
        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: orientation === 'vertical' ? 'center' : 'center',
              gap: config.spacing / 8,
              flexDirection: orientation === 'vertical' ? 'column' : 'row',
              textAlign: orientation === 'vertical' ? 'center' : 'left'
            }}
          >
            <LogoImage />
            {(variant === 'full') && (
              <Box sx={{ ml: orientation === 'horizontal' ? 1 : 0 }}>
                <TextComponent />
              </Box>
            )}
          </Box>
        );
    }
  };

  return (
    <Box
      onClick={onClick}
      className={`agri-intel-logo ${className}`}
      sx={{
        display: 'inline-flex',
        alignItems: 'center',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'transform 0.2s ease-in-out',
        '&:hover': onClick ? {
          transform: 'scale(1.02)'
        } : {}
      }}
    >
      {renderContent()}
    </Box>
  );
};

export default AgriIntelLogo;
