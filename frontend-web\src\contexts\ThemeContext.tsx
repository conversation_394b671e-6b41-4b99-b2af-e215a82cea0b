import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeProvider as MuiThemeProvider, createTheme, alpha } from '@mui/material/styles';
import { PaletteMode } from '@mui/material';

// Define available theme colors
export const themeColors = {
  // AgriIntel Brand Colors
  agriIntel: {
    name: 'AgriIntel Professional',
    primary: '#1565C0', // Deep Blue
    secondary: '#2E7D32', // Emerald Green
    accent: '#F57C00', // Warm Gold
    light: '#42A5F5',
    lighter: '#E3F2FD',
  },
  agriIntelBeta: {
    name: 'AgriIntel BETA',
    primary: '#FF9800', // Yellow/Orange for BETA
    secondary: '#1565C0', // Deep Blue
    accent: '#2E7D32', // Emerald Green
    light: '#FFB74D',
    lighter: '#FFF3E0',
  },
  agriIntelPro: {
    name: 'AgriIntel Professional',
    primary: '#4CAF50', // Green for Professional
    secondary: '#1565C0', // Deep Blue
    accent: '#F57C00', // Warm Gold
    light: '#81C784',
    lighter: '#E8F5E8',
  },

  // Blues
  teal: {
    name: 'Teal',
    primary: '#3AA99F',
    secondary: '#2A8A82',
    light: '#4FBEB4',
    lighter: '#E6F7F5',
  },
  neonTeal: {
    name: 'Neon Teal',
    primary: '#00E5CC',
    secondary: '#00B3A1',
    light: '#33FFDD',
    lighter: '#E6FFFC',
  },
  blue: {
    name: 'Blue',
    primary: '#3B82F6',
    secondary: '#2563EB',
    light: '#60A5FA',
    lighter: '#EFF6FF',
  },
  metalBlue: {
    name: 'Metal Blue',
    primary: '#4A6FA5',
    secondary: '#385785',
    light: '#6B8BB9',
    lighter: '#EDF2F9',
  },
  darkBlue: {
    name: 'Dark Blue',
    primary: '#1E3A8A',
    secondary: '#1E40AF',
    light: '#3B82F6',
    lighter: '#DBEAFE',
  },
  navy: {
    name: 'Navy',
    primary: '#172554',
    secondary: '#1E3A8A',
    light: '#2563EB',
    lighter: '#DBEAFE',
  },
  skyBlue: {
    name: 'Sky Blue',
    primary: '#0EA5E9',
    secondary: '#0284C7',
    light: '#38BDF8',
    lighter: '#E0F2FE',
  },
  azure: {
    name: 'Azure',
    primary: '#2DD4BF',
    secondary: '#14B8A6',
    light: '#5EEAD4',
    lighter: '#CCFBF1',
  },

  // Greens
  emerald: {
    name: 'Emerald',
    primary: '#10B981',
    secondary: '#059669',
    light: '#34D399',
    lighter: '#ECFDF5',
  },
  green: {
    name: 'Green',
    primary: '#10B981',
    secondary: '#059669',
    light: '#34D399',
    lighter: '#ECFDF5',
  },
  darkGreen: {
    name: 'Dark Green',
    primary: '#065F46',
    secondary: '#047857',
    light: '#10B981',
    lighter: '#D1FAE5',
  },
  lime: {
    name: 'Lime',
    primary: '#84CC16',
    secondary: '#65A30D',
    light: '#A3E635',
    lighter: '#F7FEE7',
  },
  dullGreen: {
    name: 'Dull Green',
    primary: '#6B7F59',
    secondary: '#556347',
    light: '#8A9C78',
    lighter: '#F2F5EF',
  },
  mint: {
    name: 'Mint',
    primary: '#4ADE80',
    secondary: '#22C55E',
    light: '#86EFAC',
    lighter: '#DCFCE7',
  },
  forest: {
    name: 'Forest',
    primary: '#166534',
    secondary: '#15803D',
    light: '#22C55E',
    lighter: '#DCFCE7',
  },

  // Purples
  violet: {
    name: 'Violet',
    primary: '#8B5CF6',
    secondary: '#7C3AED',
    light: '#A78BFA',
    lighter: '#F5F3FF',
  },
  purple: {
    name: 'Purple',
    primary: '#8B5CF6',
    secondary: '#7C3AED',
    light: '#A78BFA',
    lighter: '#F5F3FF',
  },
  indigo: {
    name: 'Indigo',
    primary: '#6366F1',
    secondary: '#4F46E5',
    light: '#818CF8',
    lighter: '#EEF2FF',
  },
  fuchsia: {
    name: 'Fuchsia',
    primary: '#D946EF',
    secondary: '#C026D3',
    light: '#E879F9',
    lighter: '#FDF4FF',
  },
  lavender: {
    name: 'Lavender',
    primary: '#A78BFA',
    secondary: '#8B5CF6',
    light: '#C4B5FD',
    lighter: '#EDE9FE',
  },
  plum: {
    name: 'Plum',
    primary: '#9333EA',
    secondary: '#7E22CE',
    light: '#A855F7',
    lighter: '#F3E8FF',
  },

  // Reds & Pinks
  rose: {
    name: 'Rose',
    primary: '#F43F5E',
    secondary: '#E11D48',
    light: '#FB7185',
    lighter: '#FFF1F2',
  },
  maroon: {
    name: 'Maroon',
    primary: '#9F1239',
    secondary: '#BE123C',
    light: '#E11D48',
    lighter: '#FEE2E2',
  },
  crimson: {
    name: 'Crimson',
    primary: '#DC2626',
    secondary: '#B91C1C',
    light: '#EF4444',
    lighter: '#FEE2E2',
  },
  pink: {
    name: 'Pink',
    primary: '#EC4899',
    secondary: '#DB2777',
    light: '#F472B6',
    lighter: '#FCE7F3',
  },
  hotPink: {
    name: 'Hot Pink',
    primary: '#E11D48',
    secondary: '#BE123C',
    light: '#F43F5E',
    lighter: '#FFF1F2',
  },

  // Yellows & Oranges
  amber: {
    name: 'Amber',
    primary: '#F59E0B',
    secondary: '#D97706',
    light: '#FBBF24',
    lighter: '#FFFBEB',
  },
  orange: {
    name: 'Orange',
    primary: '#F59E0B',
    secondary: '#D97706',
    light: '#FBBF24',
    lighter: '#FEF3C7',
  },
  tangerine: {
    name: 'Tangerine',
    primary: '#F97316',
    secondary: '#EA580C',
    light: '#FB923C',
    lighter: '#FFEDD5',
  },
  gold: {
    name: 'Gold',
    primary: '#EAB308',
    secondary: '#CA8A04',
    light: '#FACC15',
    lighter: '#FEF9C3',
  },
  yellow: {
    name: 'Yellow',
    primary: '#FACC15',
    secondary: '#EAB308',
    light: '#FDE047',
    lighter: '#FEF9C3',
  },

  // Earth Tones
  brown: {
    name: 'Brown',
    primary: '#92400E',
    secondary: '#B45309',
    light: '#D97706',
    lighter: '#FEF3C7',
  },
  earthBrown: {
    name: 'Earth Brown',
    primary: '#8B7355',
    secondary: '#6F5B44',
    light: '#A38F76',
    lighter: '#F6F2ED',
  },
  coffee: {
    name: 'Coffee',
    primary: '#78350F',
    secondary: '#92400E',
    light: '#B45309',
    lighter: '#FFFBEB',
  },
  sand: {
    name: 'Sand',
    primary: '#D4A76A',
    secondary: '#B78B4B',
    light: '#E2BC8A',
    lighter: '#F9F4E8',
  },

  // Neutrals
  slate: {
    name: 'Slate',
    primary: '#64748B',
    secondary: '#475569',
    light: '#94A3B8',
    lighter: '#F8FAFC',
  },
  dullBlue: {
    name: 'Dull Blue',
    primary: '#5C7D99',
    secondary: '#486478',
    light: '#7D9BB3',
    lighter: '#EFF4F8',
  },
  graphite: {
    name: 'Graphite',
    primary: '#4B5563',
    secondary: '#374151',
    light: '#6B7280',
    lighter: '#F9FAFB',
  },
  charcoal: {
    name: 'Charcoal',
    primary: '#1F2937',
    secondary: '#111827',
    light: '#374151',
    lighter: '#F3F4F6',
  },

  // Gradients (represented by their primary colors)
  sunNature: {
    name: 'Sun & Nature',
    primary: '#8A9C78',
    secondary: '#D4B942',
    light: '#F7DC6F',
    lighter: '#FFFBF0',
    gradient: 'linear-gradient(135deg, #6B7F59, #8A9C78, #D4B942, #F4D03F)',
  } as const,
  oceanBreeze: {
    name: 'Ocean Breeze',
    primary: '#0EA5E9',
    secondary: '#2DD4BF',
    light: '#38BDF8',
    lighter: '#E0F2FE',
    gradient: 'linear-gradient(135deg, #0EA5E9, #2DD4BF)',
  } as const,
  sunsetGlow: {
    name: 'Sunset Glow',
    primary: '#F97316',
    secondary: '#EC4899',
    light: '#FB923C',
    lighter: '#FFEDD5',
    gradient: 'linear-gradient(135deg, #F97316, #EC4899)',
  } as const,
  forestMist: {
    name: 'Forest Mist',
    primary: '#10B981',
    secondary: '#3B82F6',
    light: '#34D399',
    lighter: '#ECFDF5',
    gradient: 'linear-gradient(135deg, #10B981, #3B82F6)',
  } as const,
  purpleHaze: {
    name: 'Purple Haze',
    primary: '#8B5CF6',
    secondary: '#EC4899',
    light: '#A78BFA',
    lighter: '#F5F3FF',
    gradient: 'linear-gradient(135deg, #8B5CF6, #EC4899)',
  } as const,
  goldenHour: {
    name: 'Golden Hour',
    primary: '#F59E0B',
    secondary: '#EF4444',
    light: '#FBBF24',
    lighter: '#FFFBEB',
    gradient: 'linear-gradient(135deg, #F59E0B, #EF4444)',
  } as const,
  // New Navy Blue Gradients
  midnightNavy: {
    name: 'Midnight Navy',
    primary: '#0A1932',
    secondary: '#1D375A',
    light: '#2979FF',
    lighter: '#E3F2FD',
    gradient: 'linear-gradient(135deg, #0A1932, #1D375A)',
  } as const,
  deepOcean: {
    name: 'Deep Ocean',
    primary: '#0F2942',
    secondary: '#1A4980',
    light: '#4FC3F7',
    lighter: '#E1F5FE',
    gradient: 'linear-gradient(135deg, #0F2942, #1A4980)',
  } as const,
  cosmicBlue: {
    name: 'Cosmic Blue',
    primary: '#0D1B2A',
    secondary: '#1B263B',
    light: '#415A77',
    lighter: '#E0E1DD',
    gradient: 'linear-gradient(135deg, #0D1B2A, #1B263B)',
  } as const,
  azureNight: {
    name: 'Azure Night',
    primary: '#1E3A8A',
    secondary: '#3B82F6',
    light: '#60A5FA',
    lighter: '#DBEAFE',
    gradient: 'linear-gradient(135deg, #1E3A8A, #3B82F6)',
  } as const,
  sapphireGlow: {
    name: 'Sapphire Glow',
    primary: '#172554',
    secondary: '#1E40AF',
    light: '#3B82F6',
    lighter: '#DBEAFE',
    gradient: 'linear-gradient(135deg, #172554, #1E40AF)',
  } as const,
};

export type ThemeColorKey = keyof typeof themeColors;

// Define theme blend modes
export type ThemeBlendMode = 'light' | 'dark' | 'lightBlended' | 'darkBlended' | 'day' | 'night' | 'auto';

interface ThemeContextType {
  mode: PaletteMode;
  blendMode: ThemeBlendMode;
  toggleMode: () => void;
  setBlendMode: (mode: ThemeBlendMode) => void;
  currentColor: ThemeColorKey;
  setThemeColor: (color: ThemeColorKey) => void;
  theme: ReturnType<typeof createTheme>;
  availableColors: typeof themeColors;
  showThemeAnimations: boolean;
  toggleThemeAnimations: () => void;
  showBackgroundImages: boolean;
  toggleBackgroundImages: () => void;
  showRecentActivity: boolean;
  toggleRecentActivity: () => void;
  showAgentPrices: boolean;
  toggleAgentPrices: () => void;
  themeMode: PaletteMode; // Added for compatibility with CustomButton
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useThemeContext = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Load saved preferences from localStorage or use defaults
  const [mode, setMode] = useState<PaletteMode>(() => {
    const savedMode = localStorage.getItem('themeMode');
    return (savedMode as PaletteMode) || 'light';
  });

  const [blendMode, setBlendModeState] = useState<ThemeBlendMode>(() => {
    const savedBlendMode = localStorage.getItem('themeBlendMode') as ThemeBlendMode;
    return savedBlendMode || 'light';
  });

  const [currentColor, setCurrentColor] = useState<ThemeColorKey>(() => {
    const savedColor = localStorage.getItem('themeColor') as ThemeColorKey;
    return savedColor || 'sunNature'; // Set default to sunNature representing sun and nature
  });

  const [showThemeAnimations, setShowThemeAnimations] = useState<boolean>(() => {
    const saved = localStorage.getItem('showThemeAnimations');
    return saved !== null ? JSON.parse(saved) : true;
  });

  const [showBackgroundImages, setShowBackgroundImages] = useState<boolean>(() => {
    const saved = localStorage.getItem('showBackgroundImages');
    return saved !== null ? JSON.parse(saved) : false; // Default to false to use solid colors
  });

  const [showRecentActivity, setShowRecentActivity] = useState<boolean>(() => {
    const saved = localStorage.getItem('showRecentActivity');
    return saved !== null ? JSON.parse(saved) : true;
  });

  const [showAgentPrices, setShowAgentPrices] = useState<boolean>(() => {
    const saved = localStorage.getItem('showAgentPrices');
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Toggle functions
  const toggleMode = () => {
    setMode((prevMode) => (prevMode === 'light' ? 'dark' : 'light'));
  };

  const setBlendMode = (newBlendMode: ThemeBlendMode) => {
    setBlendModeState(newBlendMode);
    // Update the basic mode based on blend mode
    if (newBlendMode === 'dark' || newBlendMode === 'darkBlended') {
      setMode('dark');
    } else {
      setMode('light');
    }
  };

  const setThemeColor = (color: ThemeColorKey) => {
    setCurrentColor(color);
  };

  const toggleThemeAnimations = () => {
    setShowThemeAnimations(prev => !prev);
  };

  const toggleBackgroundImages = () => {
    setShowBackgroundImages(prev => !prev);
  };

  const toggleRecentActivity = () => {
    setShowRecentActivity(prev => !prev);
  };

  const toggleAgentPrices = () => {
    setShowAgentPrices(prev => !prev);
  };

  // Save preferences to localStorage when they change
  useEffect(() => {
    localStorage.setItem('themeMode', mode);
  }, [mode]);

  useEffect(() => {
    localStorage.setItem('themeBlendMode', blendMode);
  }, [blendMode]);

  useEffect(() => {
    localStorage.setItem('themeColor', currentColor);
  }, [currentColor]);

  useEffect(() => {
    localStorage.setItem('showThemeAnimations', JSON.stringify(showThemeAnimations));
  }, [showThemeAnimations]);

  useEffect(() => {
    localStorage.setItem('showBackgroundImages', JSON.stringify(showBackgroundImages));
  }, [showBackgroundImages]);

  useEffect(() => {
    localStorage.setItem('showRecentActivity', JSON.stringify(showRecentActivity));
  }, [showRecentActivity]);

  useEffect(() => {
    localStorage.setItem('showAgentPrices', JSON.stringify(showAgentPrices));
  }, [showAgentPrices]);

  // Get current color scheme
  const colorScheme = themeColors[currentColor];

  // Create theme based on current preferences
  const theme = React.useMemo(() => {
    // Primary color scheme
    const primaryMain = colorScheme.primary;
    const primaryDark = colorScheme.secondary;
    const primaryLight = colorScheme.light;
    const primaryLighter = colorScheme.lighter;

    // Function to get background colors based on blend mode
    const getBackgroundColor = (type: 'default' | 'paper') => {
      switch (blendMode) {
        case 'lightBlended':
          return type === 'default'
            ? `linear-gradient(135deg, ${alpha('#f8fafc', 0.95)}, ${alpha(primaryLighter, 0.1)})`
            : `linear-gradient(135deg, ${alpha('#ffffff', 0.95)}, ${alpha(primaryLighter, 0.05)})`;

        case 'darkBlended':
          return type === 'default'
            ? `linear-gradient(135deg, ${alpha('#121212', 0.95)}, ${alpha(primaryMain, 0.1)})`
            : `linear-gradient(135deg, ${alpha('#1e1e1e', 0.95)}, ${alpha(primaryMain, 0.05)})`;

        case 'day':
          return type === 'default'
            ? `linear-gradient(135deg, ${alpha('#fef3c7', 0.9)}, ${alpha(primaryLighter, 0.2)}, ${alpha('#fbbf24', 0.1)})`
            : `linear-gradient(135deg, ${alpha('#fffbeb', 0.95)}, ${alpha(primaryLighter, 0.1)})`;

        case 'night':
          return type === 'default'
            ? `linear-gradient(135deg, ${alpha('#1e1b4b', 0.95)}, ${alpha('#312e81', 0.8)}, ${alpha(primaryMain, 0.1)})`
            : `linear-gradient(135deg, ${alpha('#312e81', 0.95)}, ${alpha(primaryMain, 0.05)})`;

        case 'auto':
          const hour = new Date().getHours();
          const isDayTime = hour >= 6 && hour < 18;
          return isDayTime
            ? (type === 'default'
                ? `linear-gradient(135deg, ${alpha('#fef3c7', 0.9)}, ${alpha(primaryLighter, 0.2)})`
                : `linear-gradient(135deg, ${alpha('#fffbeb', 0.95)}, ${alpha(primaryLighter, 0.1)})`)
            : (type === 'default'
                ? `linear-gradient(135deg, ${alpha('#1e1b4b', 0.95)}, ${alpha(primaryMain, 0.1)})`
                : `linear-gradient(135deg, ${alpha('#312e81', 0.95)}, ${alpha(primaryMain, 0.05)})`);

        case 'dark':
          return type === 'default' ? '#121212' : '#1e1e1e';

        case 'light':
        default:
          return type === 'default' ? '#f8fafc' : '#ffffff';
      }
    };

    return createTheme({
      palette: {
        mode,
        primary: {
          main: primaryMain,
          light: primaryLight,
          dark: primaryDark,
          contrastText: '#ffffff',
        },
        secondary: {
          main: mode === 'dark' ? '#38B2AC' : '#38B2AC',
          light: mode === 'dark' ? '#4FD1CB' : '#4FD1CB',
          dark: mode === 'dark' ? '#2C8A84' : '#2C8A84',
          contrastText: '#ffffff',
        },
        success: {
          main: '#10b981',
          light: '#34d399',
          dark: '#059669',
          contrastText: '#ffffff',
        },
        error: {
          main: '#ef4444',
          light: '#f87171',
          dark: '#dc2626',
          contrastText: '#ffffff',
        },
        warning: {
          main: '#f59e0b',
          light: '#fbbf24',
          dark: '#d97706',
          contrastText: '#ffffff',
        },
        info: {
          main: '#3b82f6',
          light: '#60a5fa',
          dark: '#2563eb',
          contrastText: '#ffffff',
        },
        background: {
          default: getBackgroundColor('default'),
          paper: getBackgroundColor('paper'),
        },
        text: {
          primary: mode === 'dark' ? '#e2e8f0' : '#1e293b',
          secondary: mode === 'dark' ? '#94a3b8' : '#64748b',
          disabled: mode === 'dark' ? '#475569' : '#94a3b8',
        },
        divider: mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
        // Custom colors for specific use cases
        custom: {
          primaryMain,
          primaryDark,
          primaryLight,
          primaryLighter,
          cardGradient: `linear-gradient(135deg, ${primaryMain}, ${primaryDark})`,
          lightBackground: primaryLighter,
          subtleAccent: alpha(primaryMain, 0.1),
          subtleBorder: alpha(primaryMain, 0.2),
          subtleHover: alpha(primaryMain, 0.05),
          borderColor: mode === 'dark' ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)',
          // Add elevation system
          elevation: {
            0: 'none',
            1: mode === 'dark'
              ? '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.24)'
              : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            2: mode === 'dark'
              ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.24)'
              : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            3: mode === 'dark'
              ? '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)'
              : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            4: mode === 'dark'
              ? '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)'
              : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            5: mode === 'dark'
              ? '0 25px 50px -12px rgba(0, 0, 0, 0.4)'
              : '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
          },
          // Add animation presets
          animation: {
            short: '0.2s ease-in-out',
            medium: '0.3s ease-in-out',
            long: '0.5s ease-in-out',
            bounce: '0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)'
          }
        }
      },
      typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
        h1: {
          fontWeight: 700,
          fontSize: '3.2rem', // Further increased for elderly users
          letterSpacing: '-0.5px',
          lineHeight: 1.2,
          '@media (max-width:600px)': {
            fontSize: '2.7rem',
          },
        },
        h2: {
          fontWeight: 700,
          fontSize: '2.7rem', // Further increased for elderly users
          letterSpacing: '-0.5px',
          lineHeight: 1.2,
          '@media (max-width:600px)': {
            fontSize: '2.2rem',
          },
        },
        h3: {
          fontWeight: 600,
          fontSize: '2.2rem', // Further increased for elderly users
          letterSpacing: '-0.3px',
          lineHeight: 1.3,
          '@media (max-width:600px)': {
            fontSize: '1.9rem',
          },
        },
        h4: {
          fontWeight: 600,
          fontSize: '1.9rem', // Further increased for elderly users
          letterSpacing: '-0.3px',
          lineHeight: 1.3,
          '@media (max-width:600px)': {
            fontSize: '1.7rem',
          },
        },
        h5: {
          fontWeight: 600,
          fontSize: '1.7rem', // Further increased for elderly users
          letterSpacing: '-0.2px',
          lineHeight: 1.4,
          '@media (max-width:600px)': {
            fontSize: '1.5rem',
          },
        },
        h6: {
          fontWeight: 600,
          fontSize: '1.5rem', // Further increased for elderly users
          letterSpacing: '-0.2px',
          lineHeight: 1.4,
          '@media (max-width:600px)': {
            fontSize: '1.3rem',
          },
        },
        subtitle1: {
          fontSize: '1.4rem', // Further increased for elderly users
          lineHeight: 1.5,
          letterSpacing: '-0.1px',
          fontWeight: 500,
        },
        subtitle2: {
          fontSize: '1.25rem', // Further increased for elderly users
          lineHeight: 1.5,
          letterSpacing: '-0.1px',
          fontWeight: 500,
        },
        body1: {
          fontSize: '1.25rem', // Further increased for elderly users
          lineHeight: 1.6,
        },
        body2: {
          fontSize: '1.1rem', // Further increased for elderly users
          lineHeight: 1.6,
        },
        button: {
          fontWeight: 600,
          fontSize: '1.2rem', // Further increased for elderly users
          textTransform: 'none',
          letterSpacing: '0.02em',
        },
        caption: {
          fontSize: '1rem', // Further increased for elderly users
          lineHeight: 1.5,
        },
        overline: {
          fontSize: '0.95rem', // Further increased for elderly users
          fontWeight: 600,
          letterSpacing: '0.08em',
          lineHeight: 1.5,
          textTransform: 'uppercase',
        },
      },
      components: {
        // Button styles - improved for accessibility
        MuiButton: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.1)', // Stronger shadow for better visibility
              textTransform: 'none',
              fontWeight: 600,
              padding: '10px 20px', // Larger padding for easier touch targets
              transition: 'all 0.2s ease-in-out',
              fontSize: '1.1rem', // Larger font size for readability
              minHeight: '48px', // Minimum height for better touch targets
              minWidth: '100px', // Minimum width for better touch targets
            },
            contained: {
              boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.1)',
              '&:hover': {
                boxShadow: '0 6px 10px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.1)',
                transform: 'translateY(-2px)',
              },
            },
            containedPrimary: {
              background: primaryMain, // Use solid color instead of gradient
              '&:hover': {
                background: primaryMain,
                filter: 'brightness(1.15)', // More noticeable hover effect
              },
            },
            outlined: {
              borderWidth: '2px', // Thicker border for better visibility
              '&:hover': {
                borderWidth: '2px',
                backgroundColor: alpha(primaryMain, 0.1), // More noticeable hover effect
              },
            },
            text: {
              '&:hover': {
                backgroundColor: alpha(primaryMain, 0.1), // More noticeable hover effect
              },
            },
            // Size variants
            sizeSmall: {
              padding: '8px 16px',
              fontSize: '1rem',
              minHeight: '40px',
            },
            sizeLarge: {
              padding: '12px 24px',
              fontSize: '1.2rem',
              minHeight: '56px',
            },
          },
        },

        // Card styles with Enhanced Glassmorphism
        MuiCard: {
          styleOverrides: {
            root: {
              borderRadius: 16,
              background: mode === 'dark'
                ? alpha('#1a1a1a', 0.8)
                : alpha('#ffffff', 0.85),
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              border: mode === 'dark'
                ? `1px solid ${alpha('#ffffff', 0.1)}`
                : `1px solid ${alpha('#000000', 0.05)}`,
              boxShadow: mode === 'dark'
                ? '0 8px 32px 0 rgba(0, 0, 0, 0.6), 0 2px 16px 0 rgba(0, 0, 0, 0.4)'
                : '0 8px 32px 0 rgba(0, 0, 0, 0.1), 0 2px 16px 0 rgba(0, 0, 0, 0.06)',
              overflow: 'hidden',
              transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
              position: 'relative',
              '&::before': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '1px',
                background: mode === 'dark'
                  ? `linear-gradient(90deg, transparent, ${alpha('#ffffff', 0.2)}, transparent)`
                  : `linear-gradient(90deg, transparent, ${alpha(primaryMain, 0.3)}, transparent)`,
                zIndex: 1,
              },
              '&:hover': {
                transform: 'translateY(-4px) scale(1.02)',
                boxShadow: mode === 'dark'
                  ? '0 16px 48px 0 rgba(0, 0, 0, 0.8), 0 4px 24px 0 rgba(0, 0, 0, 0.6)'
                  : '0 16px 48px 0 rgba(0, 0, 0, 0.15), 0 4px 24px 0 rgba(0, 0, 0, 0.1)',
                background: mode === 'dark'
                  ? alpha('#1a1a1a', 0.9)
                  : alpha('#ffffff', 0.95),
                border: mode === 'dark'
                  ? `1px solid ${alpha('#ffffff', 0.15)}`
                  : `1px solid ${alpha(primaryMain, 0.1)}`,
              },
            },
          },
        },

        // Paper styles with Glassmorphism
        MuiPaper: {
          styleOverrides: {
            root: {
              borderRadius: 16,
              background: mode === 'dark'
                ? alpha('#1a1a1a', 0.7)
                : alpha('#ffffff', 0.8),
              backdropFilter: 'blur(16px)',
              WebkitBackdropFilter: 'blur(16px)',
              border: mode === 'dark'
                ? `1px solid ${alpha('#ffffff', 0.08)}`
                : `1px solid ${alpha('#000000', 0.04)}`,
              boxShadow: mode === 'dark'
                ? '0 6px 24px -1px rgba(0, 0, 0, 0.4), 0 2px 8px -1px rgba(0, 0, 0, 0.3)'
                : '0 6px 24px -1px rgba(0, 0, 0, 0.08), 0 2px 8px -1px rgba(0, 0, 0, 0.04)',
            },
            elevation1: {
              boxShadow: mode === 'dark'
                ? '0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.24)'
                : '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
            },
            elevation2: {
              boxShadow: mode === 'dark'
                ? '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.24)'
                : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            },
            elevation3: {
              boxShadow: mode === 'dark'
                ? '0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2)'
                : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            },
            elevation4: {
              boxShadow: mode === 'dark'
                ? '0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2)'
                : '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            },
          },
        },

        // Progress indicators
        MuiLinearProgress: {
          styleOverrides: {
            root: {
              borderRadius: 4,
              height: 8,
              backgroundColor: alpha(primaryMain, mode === 'dark' ? 0.2 : 0.1),
            },
            bar: {
              borderRadius: 4,
              background: primaryMain, // Use solid color instead of gradient
            },
          },
        },
        MuiCircularProgress: {
          styleOverrides: {
            colorPrimary: {
              color: primaryMain,
            },
          },
        },

        // Avatar styles
        MuiAvatar: {
          styleOverrides: {
            root: {
              boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
              backgroundColor: alpha(primaryMain, 0.1),
              color: primaryMain,
            },
            colorDefault: {
              backgroundColor: alpha(primaryMain, 0.1),
              color: primaryMain,
            },
          },
        },

        // Chip styles
        MuiChip: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              fontWeight: 500,
              fontSize: '0.85rem',
            },
            filled: {
              backgroundColor: alpha(primaryMain, 0.1),
              color: mode === 'dark' ? primaryLight : primaryDark,
              '&:hover': {
                backgroundColor: alpha(primaryMain, 0.2),
              },
            },
            outlined: {
              borderColor: alpha(primaryMain, 0.3),
              color: mode === 'dark' ? primaryLight : primaryMain,
              '&:hover': {
                backgroundColor: alpha(primaryMain, 0.05),
              },
            },
            deleteIcon: {
              color: mode === 'dark' ? alpha(primaryLight, 0.7) : alpha(primaryMain, 0.7),
              '&:hover': {
                color: mode === 'dark' ? primaryLight : primaryMain,
              },
            },
          },
        },

        // Table styles - improved for elderly users
        MuiTableCell: {
          styleOverrides: {
            root: {
              padding: '18px', // Increased padding
              borderBottom: `1px solid ${mode === 'dark' ? 'rgba(255, 255, 255, 0.15)' : 'rgba(0, 0, 0, 0.15)'}`,
              fontSize: '1.1rem', // Larger font size for readability
            },
            head: {
              fontWeight: 700, // Bolder headers
              backgroundColor: mode === 'dark' ? alpha(primaryMain, 0.2) : alpha(primaryMain, 0.1), // More contrast
              color: mode === 'dark' ? primaryLight : primaryDark,
              fontSize: '1.2rem', // Larger header font size
              padding: '20px 18px', // More padding in header
            },
          },
        },
        MuiTableRow: {
          styleOverrides: {
            root: {
              '&:hover': {
                backgroundColor: mode === 'dark' ? alpha(primaryMain, 0.15) : alpha(primaryMain, 0.07), // More visible hover
              },
              '&:nth-of-type(odd)': {
                backgroundColor: mode === 'dark' ? alpha(primaryMain, 0.05) : alpha(primaryMain, 0.02), // Zebra striping for better readability
              },
            },
          },
        },

        // Alerts
        MuiAlert: {
          styleOverrides: {
            root: {
              borderRadius: 8,
              padding: '12px 16px',
            },
            standardSuccess: {
              backgroundColor: mode === 'dark' ? alpha('#10b981', 0.2) : alpha('#10b981', 0.1),
              color: mode === 'dark' ? '#34d399' : '#059669',
            },
            standardError: {
              backgroundColor: mode === 'dark' ? alpha('#ef4444', 0.2) : alpha('#ef4444', 0.1),
              color: mode === 'dark' ? '#f87171' : '#dc2626',
            },
            standardWarning: {
              backgroundColor: mode === 'dark' ? alpha('#f59e0b', 0.2) : alpha('#f59e0b', 0.1),
              color: mode === 'dark' ? '#fbbf24' : '#d97706',
            },
            standardInfo: {
              backgroundColor: mode === 'dark' ? alpha('#3b82f6', 0.2) : alpha('#3b82f6', 0.1),
              color: mode === 'dark' ? '#60a5fa' : '#2563eb',
            },
          },
        },

        // Tooltips
        MuiTooltip: {
          styleOverrides: {
            tooltip: {
              backgroundColor: mode === 'dark' ? alpha('#e2e8f0', 0.9) : alpha('#1e293b', 0.9),
              color: mode === 'dark' ? '#1e293b' : '#e2e8f0',
              borderRadius: 6,
              padding: '8px 12px',
              fontSize: '0.85rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
            },
            arrow: {
              color: mode === 'dark' ? alpha('#e2e8f0', 0.9) : alpha('#1e293b', 0.9),
            },
          },
        },

        // Divider
        MuiDivider: {
          styleOverrides: {
            root: {
              borderColor: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
            },
          },
        },

        // Backdrop
        MuiBackdrop: {
          styleOverrides: {
            root: {
              backgroundColor: mode === 'dark' ? alpha('#121212', 0.8) : alpha('#f8fafc', 0.8),
              backdropFilter: 'blur(4px)',
            },
          },
        },

        // Switch
        MuiSwitch: {
          styleOverrides: {
            root: {
              width: 42,
              height: 26,
              padding: 0,
              '& .MuiSwitch-switchBase': {
                padding: 0,
                margin: 2,
                transitionDuration: '300ms',
                '&.Mui-checked': {
                  transform: 'translateX(16px)',
                  color: '#fff',
                  '& + .MuiSwitch-track': {
                    backgroundColor: primaryMain,
                    opacity: 1,
                    border: 0,
                  },
                  '&.Mui-disabled + .MuiSwitch-track': {
                    opacity: 0.5,
                  },
                },
                '&.Mui-focusVisible .MuiSwitch-thumb': {
                  color: primaryMain,
                  border: '6px solid #fff',
                },
                '&.Mui-disabled .MuiSwitch-thumb': {
                  color: mode === 'light' ? 'grey.100' : 'grey.600',
                },
                '&.Mui-disabled + .MuiSwitch-track': {
                  opacity: mode === 'light' ? 0.7 : 0.3,
                },
              },
              '& .MuiSwitch-thumb': {
                boxSizing: 'border-box',
                width: 22,
                height: 22,
              },
              '& .MuiSwitch-track': {
                borderRadius: 26 / 2,
                backgroundColor: mode === 'light' ? '#E9E9EA' : '#39393D',
                opacity: 1,
              },
            },
          },
        },

        // Tabs
        MuiTab: {
          styleOverrides: {
            root: {
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '0.95rem',
              minWidth: 100,
              padding: '12px 16px',
              '&.Mui-selected': {
                color: primaryMain,
                fontWeight: 600,
              },
            },
          },
        },
        MuiTabs: {
          styleOverrides: {
            indicator: {
              backgroundColor: primaryMain,
              height: 3,
              borderRadius: '3px 3px 0 0',
            },
          },
        },
      },
    });
  }, [mode, blendMode, colorScheme]);

  const contextValue: ThemeContextType = {
    mode,
    blendMode,
    toggleMode,
    setBlendMode,
    currentColor,
    setThemeColor,
    theme,
    availableColors: themeColors,
    showThemeAnimations,
    toggleThemeAnimations,
    showBackgroundImages,
    toggleBackgroundImages,
    showRecentActivity,
    toggleRecentActivity,
    showAgentPrices,
    toggleAgentPrices,
    themeMode: mode, // Added for compatibility with CustomButton
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <MuiThemeProvider theme={theme}>
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
