import { createTheme, alpha } from '@mui/material/styles';

/**
 * AgriIntel Livestock Management System Theme
 *
 * This theme file defines the comprehensive design system used across the entire application.
 * It uses a professional color scheme as the primary palette with carefully selected complementary colors.
 *
 * The theme includes:
 * - Modern color palette definitions with professional accents
 * - Typography settings with responsive sizes and improved readability
 * - Component style overrides for consistent UI with modern aesthetics
 * - Module-specific color schemes and gradients
 * - Elevation system for consistent depth and shadows
 * - Animation presets for consistent motion design
 * - Responsive breakpoints for adaptive layouts
 */

// Primary metallic blue color scheme
const metalBlue = {
  primary: '#4A6FA5',
  secondary: '#385785',
  light: '#6B8BB9',
  lighter: '#EDF2F9',
  dark: '#2A4A75',
  accent: '#5D82B3',
  gradient: 'linear-gradient(135deg, #4A6FA5, #385785)',
  surface: 'linear-gradient(135deg, rgba(74, 111, 165, 0.05), rgba(56, 87, 133, 0.1))'
};

// Teal color scheme for module consistency
const tealPrimary = '#4A6FA5'; // Using metallic blue as teal primary for consistency
const tealSecondary = '#385785'; // Using metallic blue secondary as teal secondary for consistency

// Elevation system - consistent shadow definitions
const elevation = {
  0: 'none',
  1: '0px 2px 4px rgba(0, 0, 0, 0.05)',
  2: '0px 4px 8px rgba(0, 0, 0, 0.08)',
  3: '0px 8px 16px rgba(0, 0, 0, 0.1)',
  4: '0px 12px 24px rgba(0, 0, 0, 0.12)',
  5: '0px 16px 32px rgba(0, 0, 0, 0.14)'
};

// Animation presets
const animation = {
  short: '0.2s ease-in-out',
  medium: '0.3s ease-in-out',
  long: '0.5s ease-in-out',
  bounce: '0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275)'
};

// Define the color palette with metallic blue as the primary color
const palette = {
  primary: {
    main: metalBlue.primary,
    light: metalBlue.light,
    dark: metalBlue.dark,
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#5D82B3',
    light: '#7B9AC5',
    dark: '#3A5A8E',
    contrastText: '#ffffff',
  },
  success: {
    main: '#10b981',
    light: '#34d399',
    dark: '#059669',
    contrastText: '#ffffff',
  },
  error: {
    main: '#ef4444',
    light: '#f87171',
    dark: '#dc2626',
    contrastText: '#ffffff',
  },
  warning: {
    main: '#f59e0b',
    light: '#fbbf24',
    dark: '#d97706',
    contrastText: '#ffffff',
  },
  info: {
    main: '#3b82f6',
    light: '#60a5fa',
    dark: '#2563eb',
    contrastText: '#ffffff',
  },
  background: {
    default: 'linear-gradient(135deg, rgba(21, 101, 192, 0.05) 0%, rgba(46, 125, 50, 0.05) 50%, rgba(245, 124, 0, 0.05) 100%)',
    paper: 'rgba(255, 255, 255, 0.9)',
    card: 'rgba(255, 255, 255, 0.85)',
    surface: 'rgba(21, 101, 192, 0.02)',
  },
  text: {
    primary: '#1e293b',
    secondary: '#64748b',
    disabled: '#94a3b8',
    accent: metalBlue.accent,
  },
  divider: 'rgba(0, 0, 0, 0.12)',
  // Custom colors for specific use cases
  custom: {
    metalBlue: metalBlue.primary,
    metalBlueSecondary: metalBlue.secondary,
    metalBlueLight: metalBlue.light,
    metalBlueLighter: metalBlue.lighter,
    metalBlueDark: metalBlue.dark,
    cardGradient: metalBlue.gradient,
    lightBackground: metalBlue.lighter,
    subtleAccent: alpha(metalBlue.primary, 0.1),
    subtleBorder: alpha(metalBlue.primary, 0.2),
    subtleHover: alpha(metalBlue.primary, 0.05),
    borderColor: 'rgba(0, 0, 0, 0.12)',
    elevation,
    animation
  }
};

// Module-specific color schemes
export const moduleColors = {
  dashboard: { primary: tealPrimary, secondary: tealSecondary },
  animals: { primary: tealPrimary, secondary: tealSecondary },
  health: { primary: tealPrimary, secondary: tealSecondary },
  breeding: { primary: tealPrimary, secondary: tealSecondary },
  feed: { primary: tealPrimary, secondary: tealSecondary },
  financial: { primary: tealPrimary, secondary: tealSecondary },
  commercial: { primary: tealPrimary, secondary: tealSecondary },
  compliance: { primary: tealPrimary, secondary: tealSecondary },
  resources: { primary: tealPrimary, secondary: tealSecondary },
};

// Module-specific background gradients (teal-based for consistency)
export const moduleBackgrounds = {
  dashboard: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  animals: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  health: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  breeding: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  feed: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  financial: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  commercial: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  compliance: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
  resources: `linear-gradient(135deg, ${alpha(tealPrimary, 0.05)}, ${alpha(tealSecondary, 0.1)})`,
};

// Create the theme
const theme = createTheme({
  palette,
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.75rem',
      letterSpacing: '-0.5px',
      lineHeight: 1.2,
      marginBottom: '0.5rem',
      '@media (max-width:600px)': {
        fontSize: '2.25rem',
      },
    },
    h2: {
      fontWeight: 700,
      fontSize: '2.25rem',
      letterSpacing: '-0.5px',
      lineHeight: 1.2,
      marginBottom: '0.5rem',
      '@media (max-width:600px)': {
        fontSize: '1.85rem',
      },
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.85rem',
      letterSpacing: '-0.3px',
      lineHeight: 1.3,
      '@media (max-width:600px)': {
        fontSize: '1.5rem',
      },
    },
    h4: {
      fontWeight: 600,
      fontSize: '1.6rem',
      letterSpacing: '-0.3px',
      lineHeight: 1.3,
      '@media (max-width:600px)': {
        fontSize: '1.35rem',
      },
    },
    h5: {
      fontWeight: 600,
      fontSize: '1.35rem',
      letterSpacing: '-0.2px',
      lineHeight: 1.4,
      '@media (max-width:600px)': {
        fontSize: '1.15rem',
      },
    },
    h6: {
      fontWeight: 600,
      fontSize: '1.15rem',
      letterSpacing: '-0.2px',
      lineHeight: 1.4,
      '@media (max-width:600px)': {
        fontSize: '1rem',
      },
    },
    subtitle1: {
      fontSize: '1.1rem',
      fontWeight: 500,
      lineHeight: 1.5,
      '@media (max-width:600px)': {
        fontSize: '1rem',
      },
    },
    subtitle2: {
      fontSize: '0.95rem',
      fontWeight: 500,
      lineHeight: 1.5,
      '@media (max-width:600px)': {
        fontSize: '0.875rem',
      },
    },
    body1: {
      fontSize: '1.1rem',
      lineHeight: 1.6,
      '@media (max-width:600px)': {
        fontSize: '1rem',
      },
    },
    body2: {
      fontSize: '0.95rem',
      lineHeight: 1.6,
      '@media (max-width:600px)': {
        fontSize: '0.875rem',
      },
    },
    button: {
      textTransform: 'none',
      fontWeight: 600,
      fontSize: '1rem',
      letterSpacing: '0.2px',
    },
    caption: {
      fontSize: '0.85rem',
      lineHeight: 1.5,
      color: palette.text.secondary,
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 600,
      letterSpacing: '1px',
      textTransform: 'uppercase',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    // Button styles
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          boxShadow: elevation[1],
          textTransform: 'none',
          fontWeight: 600,
          padding: '10px 18px',
          transition: animation.medium,
          fontSize: '0.95rem',
          minHeight: '42px',
        },
        contained: {
          boxShadow: elevation[2],
          '&:hover': {
            boxShadow: elevation[3],
            transform: 'translateY(-2px)',
          },
        },
        containedPrimary: {
          background: metalBlue.primary,
          '&:hover': {
            background: metalBlue.dark,
            filter: 'brightness(1.1)',
          },
        },
        outlined: {
          borderWidth: '1.5px',
          '&:hover': {
            borderWidth: '1.5px',
          },
        },
        text: {
          '&:hover': {
            backgroundColor: alpha(tealPrimary, 0.05),
          },
        },
      },
    },

    // Card styles
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: elevation[2],
          overflow: 'hidden',
          transition: `box-shadow ${animation.medium}, transform ${animation.medium}`,
          '&:hover': {
            boxShadow: elevation[3],
            transform: 'translateY(-4px)',
          },
          background: 'linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(245,247,250,1) 100%)',
          backdropFilter: 'blur(10px)',
        },
      },
    },
    MuiCardContent: {
      styleOverrides: {
        root: {
          padding: 24,
          '&:last-child': {
            paddingBottom: 24,
          },
        },
      },
    },
    MuiCardHeader: {
      styleOverrides: {
        root: {
          padding: '16px 24px',
        },
        title: {
          fontSize: '1.25rem',
          fontWeight: 600,
        },
        subheader: {
          fontSize: '0.875rem',
          color: palette.text.secondary,
        },
      },
    },
    MuiCardActions: {
      styleOverrides: {
        root: {
          padding: '12px 24px',
        },
      },
    },

    // Chip styles
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          fontWeight: 500,
          fontSize: '0.85rem',
          height: 28,
          '&.MuiChip-colorPrimary': {
            backgroundColor: alpha(metalBlue.primary, 0.1),
            color: metalBlue.primary,
          },
          '&.MuiChip-colorSecondary': {
            backgroundColor: alpha(metalBlue.secondary, 0.1),
            color: metalBlue.secondary,
          },
          '&.MuiChip-colorSuccess': {
            backgroundColor: alpha(palette.success.main, 0.1),
            color: palette.success.main,
          },
          '&.MuiChip-colorError': {
            backgroundColor: alpha(palette.error.main, 0.1),
            color: palette.error.main,
          },
          '&.MuiChip-colorWarning': {
            backgroundColor: alpha(palette.warning.main, 0.1),
            color: palette.warning.main,
          },
          '&.MuiChip-colorInfo': {
            backgroundColor: alpha(palette.info.main, 0.1),
            color: palette.info.main,
          },
        },
        label: {
          padding: '0 12px',
        },
      },
    },

    // Table styles
    MuiTableContainer: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: elevation[2],
          overflow: 'hidden',
          background: 'linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(245,247,250,1) 100%)',
        },
      },
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          backgroundColor: alpha(metalBlue.primary, 0.1),
          '& .MuiTableCell-head': {
            fontWeight: 600,
            color: metalBlue.dark,
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        head: {
          fontWeight: 600,
          color: palette.text.primary,
          borderBottom: `2px solid ${alpha(metalBlue.primary, 0.1)}`,
          padding: '16px',
        },
        body: {
          padding: '16px',
          borderBottom: `1px solid ${alpha(palette.divider, 0.7)}`,
        },
      },
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          transition: animation.short,
          '&:hover': {
            backgroundColor: alpha(metalBlue.primary, 0.03),
          },
        },
      },
    },

    // Paper styles
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          boxShadow: elevation[2],
          background: 'linear-gradient(135deg, rgba(255,255,255,1) 0%, rgba(245,247,250,1) 100%)',
        },
        elevation1: {
          boxShadow: elevation[1],
        },
        elevation2: {
          boxShadow: elevation[2],
        },
        elevation3: {
          boxShadow: elevation[3],
        },
        elevation4: {
          boxShadow: elevation[4],
        },
        elevation5: {
          boxShadow: elevation[5],
        },
      },
    },

    // Progress indicators
    MuiLinearProgress: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          height: 10,
          backgroundColor: alpha(metalBlue.primary, 0.1),
        },
        bar: {
          borderRadius: 8,
          background: metalBlue.gradient,
        },
      },
    },
    MuiCircularProgress: {
      styleOverrides: {
        colorPrimary: {
          color: metalBlue.primary,
        },
      },
    },

    // Avatar styles
    MuiAvatar: {
      styleOverrides: {
        root: {
          boxShadow: elevation[1],
          backgroundColor: alpha(metalBlue.primary, 0.1),
          color: metalBlue.primary,
          border: `2px solid ${alpha(metalBlue.primary, 0.2)}`,
        },
        colorDefault: {
          backgroundColor: alpha(metalBlue.primary, 0.1),
          color: metalBlue.primary,
        },
      },
    },

    // Form components
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              borderColor: alpha(palette.divider, 0.8),
              transition: 'border-color 0.2s ease-in-out',
            },
            '&:hover fieldset': {
              borderColor: alpha(metalBlue.primary, 0.5),
            },
            '&.Mui-focused fieldset': {
              borderColor: metalBlue.primary,
              borderWidth: 2,
            },
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: alpha(metalBlue.primary, 0.5),
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: metalBlue.primary,
            borderWidth: 2,
          },
        },
        notchedOutline: {
          borderColor: alpha(palette.divider, 0.8),
          transition: 'border-color 0.2s ease-in-out',
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: alpha(metalBlue.primary, 0.6),
          '&.Mui-checked': {
            color: metalBlue.primary,
          },
        },
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          color: alpha(metalBlue.primary, 0.6),
          '&.Mui-checked': {
            color: metalBlue.primary,
          },
        },
      },
    },
    MuiSwitch: {
      styleOverrides: {
        root: {
          width: 42,
          height: 26,
          padding: 0,
          margin: 8,
        },
        switchBase: {
          padding: 1,
          '&.Mui-checked': {
            transform: 'translateX(16px)',
            color: '#fff',
            '& + .MuiSwitch-track': {
              backgroundColor: metalBlue.primary,
              opacity: 1,
              border: 'none',
            },
          },
          '&.Mui-focusVisible .MuiSwitch-thumb': {
            color: metalBlue.primary,
            border: '6px solid #fff',
          },
        },
        thumb: {
          width: 24,
          height: 24,
        },
        track: {
          borderRadius: 26 / 2,
          backgroundColor: alpha(palette.text.secondary, 0.3),
          opacity: 1,
        },
      },
    },

    // Tabs
    MuiTabs: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${alpha(palette.divider, 0.2)}`,
        },
        indicator: {
          height: 3,
          borderTopLeftRadius: 3,
          borderTopRightRadius: 3,
          backgroundColor: metalBlue.primary,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          fontSize: '0.95rem',
          minWidth: 100,
          padding: '12px 16px',
          transition: 'all 0.2s ease',
          '&.Mui-selected': {
            color: metalBlue.primary,
          },
          '&:hover': {
            backgroundColor: alpha(metalBlue.primary, 0.05),
          },
        },
      },
    },

    // Alerts
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          padding: '12px 16px',
        },
        standardSuccess: {
          backgroundColor: alpha(palette.success.main, 0.1),
          color: palette.success.dark,
        },
        standardError: {
          backgroundColor: alpha(palette.error.main, 0.1),
          color: palette.error.dark,
        },
        standardWarning: {
          backgroundColor: alpha(palette.warning.main, 0.1),
          color: palette.warning.dark,
        },
        standardInfo: {
          backgroundColor: alpha(palette.info.main, 0.1),
          color: palette.info.dark,
        },
      },
    },

    // Tooltips
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: alpha(palette.text.primary, 0.9),
          borderRadius: 6,
          padding: '8px 12px',
          fontSize: '0.85rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        },
        arrow: {
          color: alpha(palette.text.primary, 0.9),
        },
      },
    },

    // Divider
    MuiDivider: {
      styleOverrides: {
        root: {
          borderColor: alpha(palette.divider, 0.6),
        },
      },
    },

    // Backdrop
    MuiBackdrop: {
      styleOverrides: {
        root: {
          backgroundColor: alpha(palette.background.paper, 0.8),
          backdropFilter: 'blur(4px)',
        },
      },
    },
  },
});

export default theme;
