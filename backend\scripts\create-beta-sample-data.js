/**
 * BETA Sample Data Creation Script
 * Creates exactly 2 realistic, interconnected South African livestock farming records
 * for each BETA-accessible module (Animals, Health, Feeding, Financial, Settings)
 */

require('dotenv').config();
const { MongoClient } = require('mongodb');
const logger = require('../src/utils/logger');

// MongoDB connection
const uri = process.env.MONGODB_URI || 'mongodb+srv://AMPD:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const dbName = process.env.MONGODB_DB_NAME || 'ampd_livestock';

// Create realistic South African livestock data
const createBetaSampleData = () => {
  const now = new Date();
  const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  // 2 Animals with South African names and realistic data
  const animals = [
    {
      _id: 'beta_animal_001',
      tagNumber: 'BETA-001',
      name: 'Tshepiso', // Tswana name meaning "Promise"
      species: 'Cattle',
      type: 'Beef',
      breed: 'Nguni',
      gender: 'Female',
      birthDate: new Date('2021-03-15'),
      status: 'Active',
      location: 'Veld A - Limpopo',
      weight: 485,
      purchaseDate: new Date('2021-04-20'),
      purchasePrice: 18500,
      healthStatus: 'healthy',
      rfidTag: 'RFID_BETA_001',
      notes: 'Excellent breeding cow, adapted to local conditions',
      imageUrl: '/images/animals/cattle-1.jpeg',
      createdBy: 'beta_user',
      createdAt: oneMonthAgo,
      updatedAt: oneWeekAgo
    },
    {
      _id: 'beta_animal_002',
      tagNumber: 'BETA-002',
      name: 'Lerato', // Sotho name meaning "Love"
      species: 'Cattle',
      type: 'Dairy',
      breed: 'Holstein',
      gender: 'Female',
      birthDate: new Date('2020-08-22'),
      status: 'Active',
      location: 'Paddock B - Gauteng',
      weight: 520,
      purchaseDate: new Date('2020-10-15'),
      purchasePrice: 22000,
      healthStatus: 'pregnant',
      rfidTag: 'RFID_BETA_002',
      notes: 'High milk producer, expecting calf in 2 months',
      imageUrl: '/images/animals/cattle-2.jpeg',
      createdBy: 'beta_user',
      createdAt: oneMonthAgo,
      updatedAt: twoWeeksAgo
    }
  ];

  // 2 Health Records linked to the animals
  const healthRecords = [
    {
      _id: 'beta_health_001',
      animalId: 'beta_animal_001',
      animalName: 'Tshepiso',
      date: twoWeeksAgo,
      type: 'vaccination',
      description: 'Annual FMD and Blackleg vaccination',
      performedBy: 'Dr. Mthembu',
      cost: 285.00,
      notes: 'Routine vaccination completed successfully. No adverse reactions observed.',
      status: 'completed',
      nextDueDate: new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000),
      createdBy: 'beta_user',
      createdAt: twoWeeksAgo,
      updatedAt: twoWeeksAgo
    },
    {
      _id: 'beta_health_002',
      animalId: 'beta_animal_002',
      animalName: 'Lerato',
      date: oneWeekAgo,
      type: 'checkup',
      description: 'Pregnancy checkup and ultrasound',
      performedBy: 'Dr. Van Der Merwe',
      cost: 450.00,
      notes: 'Pregnancy progressing well. Calf development normal. Due date estimated for early March.',
      status: 'completed',
      followUpDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000),
      createdBy: 'beta_user',
      createdAt: oneWeekAgo,
      updatedAt: oneWeekAgo
    }
  ];

  // 2 Feeding Records connected to the animals
  const feedingRecords = [
    {
      _id: 'beta_feeding_001',
      animalId: 'beta_animal_001',
      animalName: 'Tshepiso',
      date: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
      feedType: 'Lucerne Hay',
      quantity: 12,
      unit: 'kg',
      location: 'Veld A - Limpopo',
      cost: 156.00,
      notes: 'High quality lucerne for breeding cow nutrition',
      nutritionalValue: {
        protein: 18.5,
        energy: 8.2,
        fiber: 32.1
      },
      createdBy: 'beta_user',
      createdAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000)
    },
    {
      _id: 'beta_feeding_002',
      animalId: 'beta_animal_002',
      animalName: 'Lerato',
      date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      feedType: 'Dairy Concentrate',
      quantity: 8,
      unit: 'kg',
      location: 'Paddock B - Gauteng',
      cost: 224.00,
      notes: 'Special pregnancy feed with added calcium and vitamins',
      nutritionalValue: {
        protein: 16.0,
        energy: 12.5,
        calcium: 1.2
      },
      createdBy: 'beta_user',
      createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)
    }
  ];

  // 2 Financial Records related to the animals and activities
  const financialRecords = [
    {
      _id: 'beta_financial_001',
      type: 'expense',
      category: 'Health Care',
      description: 'Vaccination for Tshepiso (BETA-001)',
      amount: 285.00,
      date: twoWeeksAgo,
      relatedAnimalId: 'beta_animal_001',
      relatedAnimalName: 'Tshepiso',
      relatedRecordId: 'beta_health_001',
      paymentMethod: 'EFT',
      vendor: 'Limpopo Veterinary Services',
      invoiceNumber: 'LVS-2024-0892',
      notes: 'Annual vaccination expense',
      createdBy: 'beta_user',
      createdAt: twoWeeksAgo,
      updatedAt: twoWeeksAgo
    },
    {
      _id: 'beta_financial_002',
      type: 'expense',
      category: 'Feed',
      description: 'Feed costs for Lerato and Tshepiso',
      amount: 380.00,
      date: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      relatedAnimalId: 'beta_animal_002',
      relatedAnimalName: 'Lerato',
      relatedRecordId: 'beta_feeding_002',
      paymentMethod: 'Cash',
      vendor: 'Gauteng Feed Suppliers',
      invoiceNumber: 'GFS-2024-1156',
      notes: 'Combined feed purchase for both animals',
      breakdown: [
        { item: 'Lucerne Hay', amount: 156.00, animalId: 'beta_animal_001' },
        { item: 'Dairy Concentrate', amount: 224.00, animalId: 'beta_animal_002' }
      ],
      createdBy: 'beta_user',
      createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000)
    }
  ];

  // BETA User Settings
  const betaUserSettings = {
    _id: 'beta_user_settings',
    userId: 'beta_user',
    userTier: 'beta',
    animalLimit: 50,
    currentAnimalCount: 2,
    preferences: {
      language: 'en',
      currency: 'ZAR',
      dateFormat: 'DD/MM/YYYY',
      province: 'Limpopo',
      farmSize: 'small',
      primaryLivestock: 'cattle'
    },
    moduleAccess: {
      animals: true,
      health: true,
      feeding: true,
      financial: true,
      settings: true,
      breeding: false,
      inventory: false,
      commercial: false,
      reports: false,
      resources: false,
      compliance: false,
      analytics: false
    },
    limitations: {
      maxAnimals: 50,
      exportFormats: ['excel'],
      advancedFeatures: false,
      bulkOperations: false,
      apiAccess: false
    },
    createdAt: oneMonthAgo,
    updatedAt: now
  };

  return {
    animals,
    healthRecords,
    feedingRecords,
    financialRecords,
    betaUserSettings
  };
};

// Main function to create BETA sample data
async function createBetaData() {
  const client = new MongoClient(uri);
  
  try {
    logger.info('Creating BETA sample data...');
    await client.connect();
    const db = client.db(dbName);
    
    const sampleData = createBetaSampleData();
    
    // Clear existing BETA data
    logger.info('Clearing existing BETA sample data...');
    await db.collection('animals').deleteMany({ createdBy: 'beta_user' });
    await db.collection('health_records').deleteMany({ createdBy: 'beta_user' });
    await db.collection('feeding_records').deleteMany({ createdBy: 'beta_user' });
    await db.collection('expenses').deleteMany({ createdBy: 'beta_user' });
    await db.collection('user_settings').deleteMany({ userId: 'beta_user' });
    
    // Insert new BETA sample data
    logger.info('Inserting BETA animals...');
    await db.collection('animals').insertMany(sampleData.animals);
    
    logger.info('Inserting BETA health records...');
    await db.collection('health_records').insertMany(sampleData.healthRecords);
    
    logger.info('Inserting BETA feeding records...');
    await db.collection('feeding_records').insertMany(sampleData.feedingRecords);
    
    logger.info('Inserting BETA financial records...');
    await db.collection('expenses').insertMany(sampleData.financialRecords);
    
    logger.info('Inserting BETA user settings...');
    await db.collection('user_settings').insertOne(sampleData.betaUserSettings);
    
    logger.info('BETA sample data created successfully!');
    logger.info(`Created ${sampleData.animals.length} animals`);
    logger.info(`Created ${sampleData.healthRecords.length} health records`);
    logger.info(`Created ${sampleData.feedingRecords.length} feeding records`);
    logger.info(`Created ${sampleData.financialRecords.length} financial records`);
    logger.info('Created BETA user settings');
    
  } catch (error) {
    logger.error('Error creating BETA sample data:', error);
    throw error;
  } finally {
    await client.close();
  }
}

// Run the script
if (require.main === module) {
  createBetaData()
    .then(() => {
      console.log('✅ BETA sample data creation completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ BETA sample data creation failed:', error);
      process.exit(1);
    });
}

module.exports = { createBetaData, createBetaSampleData };
