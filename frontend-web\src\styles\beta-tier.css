/*
 * AGRIINTEL BETA V1 STARTER PACKAGE STYLES
 * Yellow/Orange branding for free trial tier
 * Professional SaaS design with upgrade prompts
 */

:root {
  /* BETA V1 Color Palette */
  --beta-primary: #FF9800;
  --beta-primary-light: #FFB74D;
  --beta-primary-dark: #F57C00;
  --beta-secondary: #FFA726;
  --beta-accent: #FFCC02;
  
  /* BETA Gradients */
  --beta-gradient-primary: linear-gradient(135deg, #FF9800 0%, #FFA726 100%);
  --beta-gradient-secondary: linear-gradient(135deg, #FFA726 0%, #FFCC02 100%);
  --beta-gradient-warm: linear-gradient(135deg, #FF8F00 0%, #FF9800 50%, #FFA726 100%);
  
  /* BETA UI Colors */
  --beta-bg-light: rgba(255, 152, 0, 0.1);
  --beta-bg-medium: rgba(255, 152, 0, 0.2);
  --beta-border: rgba(255, 152, 0, 0.3);
  --beta-text-on-primary: #FFFFFF;
  --beta-text-secondary: #E65100;
  
  /* Upgrade Prompt Colors */
  --upgrade-bg: #FFF3E0;
  --upgrade-border: #FFB74D;
  --upgrade-text: #E65100;
  --upgrade-button: #FF9800;
  --upgrade-button-hover: #F57C00;
}

/* BETA Login Page Styling */
.beta-login-container {
  background: var(--beta-gradient-primary);
  min-height: 100vh;
  position: relative;
}

.beta-login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url('../../public/images/animals/cattle-1.jpeg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  opacity: 0.2;
  z-index: 0;
}

.beta-login-card {
  background: rgba(255, 255, 255, 1) !important;
  border: 2px solid var(--beta-border) !important;
  border-radius: 16px !important;
  box-shadow: 0 12px 40px rgba(255, 152, 0, 0.25) !important;
}

.beta-brand-badge {
  background: var(--beta-gradient-primary) !important;
  color: var(--beta-text-on-primary) !important;
  font-weight: 700 !important;
  font-size: 0.875rem !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  margin-bottom: 1rem !important;
  display: inline-block;
  box-shadow: 0 2px 8px rgba(255, 152, 0, 0.3);
}

.beta-login-title {
  color: var(--beta-primary-dark) !important;
  font-weight: 800 !important;
  margin-bottom: 0.5rem !important;
}

.beta-login-subtitle {
  color: var(--beta-text-secondary) !important;
  margin-bottom: 2rem !important;
}

.beta-login-button {
  background: var(--beta-gradient-primary) !important;
  color: var(--beta-text-on-primary) !important;
  font-weight: 600 !important;
  border-radius: 8px !important;
  padding: 12px 24px !important;
  text-transform: none !important;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.beta-login-button:hover {
  background: var(--beta-gradient-secondary) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 152, 0, 0.4) !important;
}

/* BETA Dashboard Styling */
.beta-dashboard-header {
  background: var(--beta-gradient-primary) !important;
  color: var(--beta-text-on-primary) !important;
  padding: 1rem 2rem !important;
  border-radius: 0 0 16px 16px !important;
  margin-bottom: 2rem !important;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2) !important;
}

.beta-dashboard-title {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.beta-tier-badge {
  background: rgba(255, 255, 255, 0.2) !important;
  color: var(--beta-text-on-primary) !important;
  font-weight: 700 !important;
  font-size: 0.75rem !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* BETA Sidebar Styling */
.beta-sidebar {
  background: var(--beta-gradient-warm) !important;
}

.beta-sidebar .MuiListItemButton-root {
  color: var(--beta-text-on-primary) !important;
}

.beta-sidebar .MuiListItemButton-root:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.beta-sidebar .MuiListItemButton-root.Mui-selected {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* BETA Module Cards */
.beta-module-card {
  border: 2px solid var(--beta-border) !important;
  border-radius: 12px !important;
  background: var(--beta-bg-light) !important;
  transition: all 0.3s ease !important;
}

.beta-module-card:hover {
  border-color: var(--beta-primary) !important;
  background: var(--beta-bg-medium) !important;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(255, 152, 0, 0.2) !important;
}

.beta-module-icon {
  color: var(--beta-primary) !important;
  font-size: 3rem !important;
  margin-bottom: 1rem !important;
}

.beta-module-title {
  color: var(--beta-primary-dark) !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important;
}

.beta-module-description {
  color: var(--beta-text-secondary) !important;
  font-size: 0.875rem !important;
}

/* Upgrade Prompts */
.beta-upgrade-prompt {
  background: var(--upgrade-bg) !important;
  border: 2px solid var(--upgrade-border) !important;
  border-radius: 12px !important;
  padding: 1.5rem !important;
  margin: 1rem 0 !important;
  text-align: center;
}

.beta-upgrade-title {
  color: var(--upgrade-text) !important;
  font-weight: 700 !important;
  font-size: 1.125rem !important;
  margin-bottom: 0.5rem !important;
}

.beta-upgrade-description {
  color: var(--upgrade-text) !important;
  margin-bottom: 1rem !important;
  font-size: 0.875rem !important;
}

.beta-upgrade-button {
  background: var(--upgrade-button) !important;
  color: var(--beta-text-on-primary) !important;
  font-weight: 600 !important;
  padding: 0.75rem 2rem !important;
  border-radius: 8px !important;
  text-transform: none !important;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
}

.beta-upgrade-button:hover {
  background: var(--upgrade-button-hover) !important;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 152, 0, 0.4) !important;
}

/* BETA Feature Lock Overlay */
.beta-feature-lock {
  position: relative;
  overflow: hidden;
}

.beta-feature-lock::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 152, 0, 0.1);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  z-index: 1;
}

.beta-feature-lock-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
  text-align: center;
  background: var(--upgrade-bg);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--upgrade-border);
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
}

/* BETA Navigation Restrictions */
.beta-restricted-module {
  opacity: 0.6;
  pointer-events: none;
  position: relative;
}

.beta-restricted-module::after {
  content: '🔒 Pro Feature';
  position: absolute;
  top: 50%;
  right: 1rem;
  transform: translateY(-50%);
  background: var(--upgrade-button);
  color: white;
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
}

/* BETA Metrics and Stats */
.beta-metric-card {
  background: var(--beta-bg-light) !important;
  border: 1px solid var(--beta-border) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
}

.beta-metric-value {
  color: var(--beta-primary-dark) !important;
  font-weight: 700 !important;
  font-size: 1.5rem !important;
}

.beta-metric-label {
  color: var(--beta-text-secondary) !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
}

/* BETA Responsive Design */
@media (max-width: 768px) {
  .beta-dashboard-header {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
  }
  
  .beta-upgrade-prompt {
    padding: 1rem !important;
    margin: 0.5rem 0 !important;
  }
  
  .beta-module-card {
    margin-bottom: 1rem !important;
  }
}

/* BETA Animation Effects */
@keyframes beta-pulse {
  0% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(255, 152, 0, 0); }
  100% { box-shadow: 0 0 0 0 rgba(255, 152, 0, 0); }
}

.beta-pulse-animation {
  animation: beta-pulse 2s infinite;
}

/* BETA Focus States for Accessibility */
.beta-login-container *:focus,
.beta-dashboard-header *:focus,
.beta-module-card *:focus {
  outline: 2px solid var(--beta-primary) !important;
  outline-offset: 2px;
}

/* High Contrast Mode Support for BETA */
@media (prefers-contrast: high) {
  :root {
    --beta-primary: #E65100;
    --beta-primary-light: #FF8F00;
    --beta-primary-dark: #BF360C;
  }
}

/* Reduced Motion Support for BETA */
@media (prefers-reduced-motion: reduce) {
  .beta-module-card,
  .beta-login-button,
  .beta-upgrade-button {
    transition: none !important;
  }
  
  .beta-pulse-animation {
    animation: none !important;
  }
}
