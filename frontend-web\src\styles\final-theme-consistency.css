/**
 * Final Theme Consistency Override
 * Ensures complete visual consistency across the entire AgriIntel application
 * Removes ALL white backgrounds and applies unified tinted blends
 */

/* ===== GLOBAL OVERRIDES ===== */

/* Ensure no white backgrounds anywhere */
* {
  background-color: transparent !important;
}

/* Root application background */
html, body, #root {
  background: linear-gradient(135deg,
    rgba(21, 101, 192, 0.08) 0%,
    rgba(46, 125, 50, 0.08) 50%,
    rgba(245, 124, 0, 0.08) 100%
  ), url('/images/modules/animals/cattle-2.jpeg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
}

/* ===== MATERIAL-UI COMPONENT OVERRIDES ===== */

/* All Material-UI Paper components */
.MuiPaper-root {
  background: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* Cards */
.MuiCard-root {
  background: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
}

/* Dialogs and Modals */
.MuiDialog-paper,
.MuiModal-root .MuiPaper-root {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(30px) !important;
  -webkit-backdrop-filter: blur(30px) !important;
}

/* Drawers and Sidebars */
.MuiDrawer-paper {
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Form Controls */
.MuiTextField-root .MuiOutlinedInput-root,
.MuiSelect-root,
.MuiFormControl-root .MuiOutlinedInput-root {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Tables */
.MuiTableContainer-root {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

.MuiTableHead-root {
  background: rgba(21, 101, 192, 0.1) !important;
}

.MuiTableRow-root:nth-of-type(even) {
  background: rgba(255, 255, 255, 0.5) !important;
}

/* Menus and Popovers */
.MuiMenu-paper,
.MuiPopover-paper {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* ===== CUSTOM COMPONENT OVERRIDES ===== */

/* Dashboard components */
.dashboard-card,
.module-card,
.stats-card,
.info-card,
.metric-card,
.overview-card {
  background: rgba(255, 255, 255, 0.85) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* Navigation components */
.nav-container,
.navigation-bar,
.top-nav,
.main-nav {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Sidebar components */
.sidebar,
.side-nav,
.navigation-sidebar {
  background: rgba(255, 255, 255, 0.15) !important;
  backdrop-filter: blur(25px) !important;
  -webkit-backdrop-filter: blur(25px) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Content areas */
.main-content,
.page-content,
.module-content,
.dashboard-content {
  background: transparent !important;
}

/* Form containers */
.form-container,
.form-wrapper,
.input-group {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  margin-bottom: 1rem !important;
}

/* ===== LANDING PAGE SPECIFIC ===== */

.agri-landing,
.landing-container,
.enhanced-landing-container {
  background: linear-gradient(135deg,
    rgba(46, 125, 50, 0.9) 0%,
    rgba(21, 101, 192, 0.8) 50%,
    rgba(245, 124, 0, 0.7) 100%
  ), url('/images/animals/cattle-1.jpeg') !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
}

/* Landing page sections */
.landing-section,
.hero-section,
.features-section,
.pricing-section {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  margin: 1rem 0 !important;
}

/* ===== LOGIN PAGE SPECIFIC ===== */

.login-container,
.split-screen-login,
.login-form-panel {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 768px) {
  html, body, #root {
    background-attachment: scroll !important;
  }
  
  .agri-landing {
    background-attachment: scroll !important;
  }
}

/* ===== ACCESSIBILITY OVERRIDES ===== */

@media (prefers-reduced-motion: reduce) {
  * {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
}

@media (prefers-contrast: high) {
  .MuiPaper-root,
  .MuiCard-root,
  .dashboard-card {
    background: rgba(255, 255, 255, 0.98) !important;
    border: 2px solid #000000 !important;
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  * {
    background: white !important;
    background-image: none !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }
}
