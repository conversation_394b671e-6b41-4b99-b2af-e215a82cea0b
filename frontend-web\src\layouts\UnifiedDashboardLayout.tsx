import React, { useState, useEffect } from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Box, Toolbar, IconButton, Typography, Menu, MenuItem, Divider, useMediaQuery, alpha, AppBar } from '@mui/material';
import {
  Menu as MenuIcon,
  AccountCircle,
  Notifications,
  Search,
  Settings,
  Brightness4,
  Brightness7,
  Dashboard,
  ExitToApp,
  Upgrade
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme as useMuiTheme } from '@mui/material/styles';
import { useThemeContext, ThemeColorKey, themeColors } from '../contexts/ThemeContext';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { Palette, ColorLens } from '@mui/icons-material';
import Sidebar from '../components/Sidebar';
import GlobalSearch from '../components/GlobalSearch';
import NotificationsPanel from '../components/NotificationsPanel';
import WeatherWidget from '../components/WeatherWidget';
import QuickActions from '../components/QuickActions';
import GlobalBackground from '../components/common/GlobalBackground';
import GlobalTabSelectionFixer from '../components/common/GlobalTabSelectionFixer';
import BetaBadge from '../components/beta/BetaBadge';

/**
 * UnifiedDashboardLayout - A consistent layout component for the entire application
 * Combines the best features of DashboardLayout and EnhancedDashboardLayout
 */
const UnifiedDashboardLayout: React.FC = () => {
  // State
  const [sidebarOpen, setSidebarOpen] = useState(true); // Always expanded by default
  const [profileMenuAnchor, setProfileMenuAnchor] = useState<null | HTMLElement>(null);
  const [mobileMenuAnchor, setMobileMenuAnchor] = useState<null | HTMLElement>(null);
  const [themeMenuAnchor, setThemeMenuAnchor] = useState<null | HTMLElement>(null);
  const [searchOpen, setSearchOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [weatherOpen, setWeatherOpen] = useState(false);
  const [quickActionsOpen, setQuickActionsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Hooks
  const location = useLocation();
  const navigate = useNavigate();
  const muiTheme = useMuiTheme();
  const { theme, toggleMode, currentColor, setThemeColor } = useThemeContext();
  const { translate, language, changeLanguage } = useLanguage();
  const { user, logout } = useAuth();

  const isMobile = useMediaQuery(muiTheme.breakpoints.down('md'));
  const isTablet = useMediaQuery(muiTheme.breakpoints.down('lg'));

  // Check if we're in BETA mode
  const isBetaMode = location.pathname.startsWith('/beta-dashboard');

  // Keep sidebar open by default, even on mobile
  useEffect(() => {
    // Only close on mobile if explicitly set by user
    if (isMobile && window.innerWidth < 768) {
      // Don't automatically close the sidebar
    }
  }, [location.pathname, isMobile]);

  // Simulate loading resources
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Toggle functions
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const toggleSearch = () => {
    setSearchOpen(!searchOpen);
  };

  const toggleNotifications = () => {
    setNotificationsOpen(!notificationsOpen);
  };

  const toggleWeather = () => {
    setWeatherOpen(!weatherOpen);
  };

  const toggleQuickActions = () => {
    setQuickActionsOpen(!quickActionsOpen);
  };

  // Handle profile menu
  const handleProfileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setProfileMenuAnchor(event.currentTarget);
  };

  const handleProfileMenuClose = () => {
    setProfileMenuAnchor(null);
  };

  // Handle theme menu
  const handleThemeMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setThemeMenuAnchor(event.currentTarget);
  };

  const handleThemeMenuClose = () => {
    setThemeMenuAnchor(null);
  };

  const handleThemeChange = (color: string) => {
    setThemeColor(color as ThemeColorKey);
    handleThemeMenuClose();
  };

  // Handle mobile menu
  const handleMobileMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setMobileMenuAnchor(event.currentTarget);
  };

  const handleMobileMenuClose = () => {
    setMobileMenuAnchor(null);
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    navigate('/login');
    handleProfileMenuClose();
  };

  // Render theme menu
  const renderThemeMenu = (
    <Menu
      anchorEl={themeMenuAnchor}
      id="theme-menu"
      keepMounted
      open={Boolean(themeMenuAnchor)}
      onClose={handleThemeMenuClose}
      PaperProps={{
        sx: {
          mt: 1.5,
          borderRadius: 2,
          boxShadow: '0 8px 30px rgba(0,0,0,0.15)',
          border: `1px solid ${alpha(muiTheme.palette.primary.main, 0.1)}`,
          minWidth: 280,
          maxHeight: '80vh',
          overflowY: 'auto',
          background: alpha(muiTheme.palette.background.paper, 0.9),
          backdropFilter: 'blur(10px)'
        }
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <Box sx={{ px: 2, py: 1.5 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold', mb: 1 }}>
          {translate('settings.theme.title')}
        </Typography>
      </Box>
      <Divider />

      {/* Navy Blue Gradients */}
      <Box sx={{ px: 2, py: 1 }}>
        <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
          Navy Blue Gradients
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {['midnightNavy', 'deepOcean', 'cosmicBlue', 'azureNight', 'sapphireGlow'].map((color) => (
            <Box
              key={color}
              onClick={() => handleThemeChange(color)}
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                cursor: 'pointer',
                background: themeColors[color as ThemeColorKey].gradient ||
                  `linear-gradient(135deg, ${themeColors[color as ThemeColorKey].primary}, ${themeColors[color as ThemeColorKey].secondary})`,
                border: currentColor === color ? `2px solid ${muiTheme.palette.primary.main}` : '2px solid transparent',
                boxShadow: currentColor === color ? `0 0 0 2px ${alpha(muiTheme.palette.primary.main, 0.3)}` : 'none',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  boxShadow: `0 4px 10px ${alpha(muiTheme.palette.common.black, 0.2)}`
                }
              }}
            />
          ))}
        </Box>
      </Box>

      <Divider sx={{ my: 1 }} />

      {/* Other Gradients */}
      <Box sx={{ px: 2, py: 1 }}>
        <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
          Other Gradients
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {['oceanBreeze', 'sunsetGlow', 'forestMist', 'purpleHaze', 'goldenHour'].map((color) => (
            <Box
              key={color}
              onClick={() => handleThemeChange(color)}
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                cursor: 'pointer',
                background: themeColors[color as ThemeColorKey].gradient ||
                  `linear-gradient(135deg, ${themeColors[color as ThemeColorKey].primary}, ${themeColors[color as ThemeColorKey].secondary})`,
                border: currentColor === color ? `2px solid ${muiTheme.palette.primary.main}` : '2px solid transparent',
                boxShadow: currentColor === color ? `0 0 0 2px ${alpha(muiTheme.palette.primary.main, 0.3)}` : 'none',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  boxShadow: `0 4px 10px ${alpha(muiTheme.palette.common.black, 0.2)}`
                }
              }}
            />
          ))}
        </Box>
      </Box>

      <Divider sx={{ my: 1 }} />

      {/* Solid Colors */}
      <Box sx={{ px: 2, py: 1 }}>
        <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
          Solid Colors
        </Typography>
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          {['blue', 'navy', 'teal', 'emerald', 'violet', 'purple', 'rose', 'amber'].map((color) => (
            <Box
              key={color}
              onClick={() => handleThemeChange(color)}
              sx={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                cursor: 'pointer',
                backgroundColor: themeColors[color as ThemeColorKey].primary,
                border: currentColor === color ? `2px solid ${muiTheme.palette.primary.main}` : '2px solid transparent',
                boxShadow: currentColor === color ? `0 0 0 2px ${alpha(muiTheme.palette.primary.main, 0.3)}` : 'none',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  boxShadow: `0 4px 10px ${alpha(muiTheme.palette.common.black, 0.2)}`
                }
              }}
            />
          ))}
        </Box>
      </Box>

      <Divider />
      <MenuItem onClick={() => navigate('/settings/theme')}>
        <Settings fontSize="small" sx={{ mr: 1.5 }} /> {translate('settings.theme.moreOptions')}
      </MenuItem>
    </Menu>
  );

  // Render profile menu
  const renderProfileMenu = (
    <Menu
      anchorEl={profileMenuAnchor}
      id="profile-menu"
      keepMounted
      open={Boolean(profileMenuAnchor)}
      onClose={handleProfileMenuClose}
      PaperProps={{
        sx: {
          mt: 1.5,
          borderRadius: 2,
          boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
          border: `1px solid ${alpha(muiTheme.palette.primary.main, 0.1)}`,
          minWidth: 200
        }
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <Box sx={{ px: 2, py: 1.5 }}>
        <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>
          {user?.name || user?.username || translate('settings.users')}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {user?.email || '<EMAIL>'}
        </Typography>
        {isBetaMode && (
          <Box
            sx={{
              background: '#FFC107',
              color: '#000',
              px: 1,
              py: 0.3,
              borderRadius: 1,
              fontSize: '0.7rem',
              fontWeight: 'bold',
              mt: 0.5,
              display: 'inline-block'
            }}
          >
            BETA USER
          </Box>
        )}
      </Box>
      <Divider />
      <MenuItem onClick={() => { handleProfileMenuClose(); navigate('/dashboard'); }}>
        <Dashboard fontSize="small" sx={{ mr: 1.5 }} /> {translate('nav.dashboard')}
      </MenuItem>
      <MenuItem onClick={() => { handleProfileMenuClose(); navigate('/settings/profile'); }}>
        <AccountCircle fontSize="small" sx={{ mr: 1.5 }} /> {translate('nav.profile')}
      </MenuItem>
      <MenuItem onClick={() => { handleProfileMenuClose(); navigate(isBetaMode ? '/beta-dashboard/settings' : '/settings'); }}>
        <Settings fontSize="small" sx={{ mr: 1.5 }} /> {translate('nav.settings')}
      </MenuItem>
      {isBetaMode && (
        <>
          <Divider />
          <MenuItem
            onClick={() => { handleProfileMenuClose(); navigate('/login'); }}
            sx={{
              color: '#FFC107',
              '&:hover': {
                background: alpha('#FFC107', 0.1)
              }
            }}
          >
            <Upgrade fontSize="small" sx={{ mr: 1.5 }} /> Upgrade to Premium
          </MenuItem>
        </>
      )}
      <Divider />
      <MenuItem onClick={handleLogout}>
        <ExitToApp fontSize="small" sx={{ mr: 1.5 }} /> {translate('nav.logout')}
      </MenuItem>
    </Menu>
  );

  // Render mobile menu
  const renderMobileMenu = (
    <Menu
      anchorEl={mobileMenuAnchor}
      id="mobile-menu"
      keepMounted
      open={Boolean(mobileMenuAnchor)}
      onClose={handleMobileMenuClose}
      PaperProps={{
        sx: {
          mt: 1.5,
          borderRadius: 2,
          boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
          border: `1px solid ${alpha(muiTheme.palette.primary.main, 0.1)}`
        }
      }}
      transformOrigin={{ horizontal: 'right', vertical: 'top' }}
      anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
    >
      <MenuItem onClick={toggleSearch}>
        <IconButton size="large" color="inherit">
          <Search />
        </IconButton>
        <p>{translate('nav.search')}</p>
      </MenuItem>
      <MenuItem onClick={toggleNotifications}>
        <IconButton size="large" color="inherit">
          <Notifications />
        </IconButton>
        <p>{translate('nav.notifications')}</p>
      </MenuItem>
      <MenuItem onClick={toggleMode}>
        <IconButton size="large" color="inherit">
          {muiTheme.palette.mode === 'dark' ? <Brightness7 /> : <Brightness4 />}
        </IconButton>
        <p>{translate('nav.theme')}</p>
      </MenuItem>
      <MenuItem onClick={handleProfileMenuOpen}>
        <IconButton
          size="large"
          edge="end"
          aria-haspopup="true"
          color="inherit"
        >
          <AccountCircle />
        </IconButton>
        <p>{translate('nav.profile')}</p>
      </MenuItem>
    </Menu>
  );

  if (isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-primary-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-primary-800">{translate('common.loading')} {translate('dashboard.welcome')}</h2>
          <p className="text-gray-600">{translate('dashboard.preparing')}</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <GlobalTabSelectionFixer />
      <Box sx={{
      display: 'flex',
      minHeight: '100vh',
      overflow: 'visible',
      // Use AgriIntel colors for gradient background
      background: `linear-gradient(135deg,
        rgba(21, 101, 192, 0.08) 0%,
        rgba(46, 125, 50, 0.08) 25%,
        rgba(245, 124, 0, 0.08) 50%,
        rgba(46, 125, 50, 0.08) 75%,
        rgba(21, 101, 192, 0.08) 100%
      )`,
      margin: 0,
      padding: 0,
      position: 'relative',
      // Add subtle animated gradient overlay
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 15% 50%, ${alpha(themeColors[currentColor as ThemeColorKey].light, 0.2)} 0%, transparent 50%),
                    radial-gradient(circle at 85% 30%, ${alpha(themeColors[currentColor as ThemeColorKey].primary, 0.1)} 0%, transparent 50%)`,
        animation: 'gradientShift 15s ease-in-out infinite alternate',
        zIndex: 0,
        opacity: 0.6,
        pointerEvents: 'none', // Ensure the overlay doesn't block clicks
      },
      '@keyframes gradientShift': {
        '0%': { backgroundPosition: '0% 50%' },
        '50%': { backgroundPosition: '100% 50%' },
        '100%': { backgroundPosition: '0% 50%' }
      },
    }}>
      {/* App Bar - Using the same color scheme as sidebar for better blending */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: (theme) => theme.zIndex.drawer + 1,
          transition: 'all 0.3s ease',
          // Use theme colors for gradient background
          background: `linear-gradient(135deg, ${themeColors[currentColor as ThemeColorKey].primary}, ${themeColors[currentColor as ThemeColorKey].secondary})`,
          backdropFilter: 'blur(10px)',
          boxShadow: 'none',
          borderBottom: 'none',
          // Add subtle pulsing glow effect
          '&::after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '2px',
            background: `linear-gradient(90deg,
              ${alpha(themeColors[currentColor as ThemeColorKey].light, 0)},
              ${alpha(themeColors[currentColor as ThemeColorKey].light, 0.7)},
              ${alpha(themeColors[currentColor as ThemeColorKey].light, 0)})`,
            backgroundSize: '200% 100%',
            animation: 'glowPulse 3s ease-in-out infinite',
            pointerEvents: 'none', // Ensure the glow effect doesn't block clicks
          },
          '@keyframes glowPulse': {
            '0%': { backgroundPosition: '0% 50%' },
            '50%': { backgroundPosition: '100% 50%' },
            '100%': { backgroundPosition: '0% 50%' }
          },
          '& .MuiToolbar-root': {
            position: 'relative',
            zIndex: 1200,
            pointerEvents: 'auto'
          }
        }}
      >
        <Toolbar>
          {/* Menu Icon */}
          <IconButton
            edge="start"
            color="inherit"
            aria-label="open drawer"
            onClick={toggleSidebar}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          {/* Logo with BETA indicator */}
          <Box sx={{ display: { xs: 'none', sm: 'flex' }, alignItems: 'center', gap: 1 }}>
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                fontWeight: 'bold',
                color: 'white'
              }}
            >
              AgriIntel
            </Typography>
            {isBetaMode && (
              <Box
                sx={{
                  background: '#FFC107',
                  color: '#000',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  fontSize: '0.7rem',
                  fontWeight: 'bold'
                }}
              >
                BETA
              </Box>
            )}
          </Box>

          {/* Spacer */}
          <Box sx={{ flexGrow: 1 }} />

          {/* Desktop Icons */}
          <Box sx={{ display: { xs: 'none', md: 'flex' }, alignItems: 'center', gap: 1 }}>
            {/* BETA Upgrade Button */}
            {isBetaMode && (
              <IconButton
                size="large"
                color="inherit"
                onClick={() => navigate('/login')}
                sx={{
                  mx: 0.5,
                  background: alpha('#FFC107', 0.2),
                  color: '#FFC107',
                  transition: 'all 0.2s',
                  '&:hover': {
                    background: alpha('#FFC107', 0.3),
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                <Upgrade />
              </IconButton>
            )}

            {/* Search */}
            <IconButton
              size="large"
              color="inherit"
              onClick={toggleSearch}
              sx={{
                mx: 0.5,
                transition: 'all 0.2s',
                '&:hover': {
                  background: alpha(muiTheme.palette.common.white, 0.15),
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Search />
            </IconButton>

            {/* Notifications */}
            <IconButton
              size="large"
              color="inherit"
              onClick={toggleNotifications}
              sx={{
                mx: 0.5,
                transition: 'all 0.2s',
                '&:hover': {
                  background: alpha(muiTheme.palette.common.white, 0.15),
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Notifications />
            </IconButton>

            {/* Theme Toggle */}
            <IconButton
              size="large"
              color="inherit"
              onClick={toggleMode}
              sx={{
                mx: 0.5,
                transition: 'all 0.2s',
                '&:hover': {
                  background: alpha(muiTheme.palette.common.white, 0.15),
                  transform: 'translateY(-2px)'
                }
              }}
            >
              {muiTheme.palette.mode === 'dark' ? <Brightness7 /> : <Brightness4 />}
            </IconButton>

            {/* Theme Selector */}
            <IconButton
              size="large"
              color="inherit"
              onClick={handleThemeMenuOpen}
              sx={{
                mx: 0.5,
                transition: 'all 0.2s',
                position: 'relative',
                overflow: 'hidden',
                '&:hover': {
                  background: alpha(muiTheme.palette.common.white, 0.15),
                  transform: 'translateY(-2px)'
                },
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: themeColors[currentColor as ThemeColorKey].gradient ||
                    `linear-gradient(135deg, ${themeColors[currentColor as ThemeColorKey].primary}, ${themeColors[currentColor as ThemeColorKey].secondary})`,
                  opacity: 0.3,
                  zIndex: -1,
                  borderRadius: '50%'
                }
              }}
            >
              <Palette />
            </IconButton>

            {/* BETA Badge for BETA users */}
            {user?.role === 'beta' && (
              <Box sx={{ mr: 1 }}>
                <BetaBadge variant="small" animated={true} />
              </Box>
            )}

            {/* Profile */}
            <IconButton
              size="large"
              edge="end"
              aria-haspopup="true"
              onClick={handleProfileMenuOpen}
              color="inherit"
              sx={{
                ml: 0.5,
                transition: 'all 0.2s',
                '&:hover': {
                  background: alpha(muiTheme.palette.common.white, 0.15),
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <AccountCircle />
            </IconButton>
          </Box>

          {/* Mobile Menu Icon */}
          <Box sx={{ display: { xs: 'flex', md: 'none' } }}>
            <IconButton
              size="large"
              aria-haspopup="true"
              onClick={handleMobileMenuOpen}
              color="inherit"
            >
              <MenuIcon />
            </IconButton>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Render menus */}
      {renderProfileMenu}
      {renderThemeMenu}
      {renderMobileMenu}

      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
        isMobile={isMobile}
      />

      {/* Main Content - Seamlessly blended with sidebar */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          width: { sm: `calc(100% - ${sidebarOpen ? 300 : 70}px)` },
          transition: 'all 0.3s ease',
          mt: '64px',
          position: 'relative',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column',
          height: 'calc(100vh - 64px)',
          backgroundColor: 'transparent',
          background: 'transparent',
          borderLeft: 'none',
          marginLeft: 0,
          paddingLeft: 0,
          boxShadow: 'none',
          zIndex: 1,
          // Create a gradient that matches the sidebar exactly on the left edge
          backgroundImage: 'linear-gradient(to right, rgba(8, 20, 40, 0.98) 0%, rgba(15, 35, 70, 0.95) 5%, transparent 15%)',
          // Add subtle animated background
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'radial-gradient(circle at 30% 50%, rgba(41, 121, 255, 0.05) 0%, transparent 50%), radial-gradient(circle at 70% 50%, rgba(25, 65, 125, 0.05) 0%, transparent 50%)',
            backgroundSize: '200% 200%',
            animation: 'gradientFloat 15s ease-in-out infinite alternate',
            zIndex: -1
          },
          '@keyframes gradientFloat': {
            '0%': { backgroundPosition: '0% 0%' },
            '50%': { backgroundPosition: '100% 100%' },
            '100%': { backgroundPosition: '0% 0%' }
          }
        }}
      >
        {/* Global Background with Content */}
        <GlobalBackground
          pattern="mesh"
          animated={true}
          opacity={0.05}
          accentColor="rgba(41, 121, 255, 0.7)"
          secondaryColor="rgba(25, 65, 125, 0.7)"
        >
          <Box sx={{
            flexGrow: 1,
            overflow: 'visible',
            p: 3,
            pb: 5,
            minHeight: '100%',
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '16px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            position: 'relative',
            // Add subtle animated glow effect
            '&::after': {
              content: '""',
              position: 'absolute',
              top: '5%',
              left: '5%',
              width: '90%',
              height: '90%',
              background: 'radial-gradient(circle at center, rgba(41, 121, 255, 0.03) 0%, transparent 70%)',
              filter: 'blur(40px)',
              animation: 'pulseGlow 8s ease-in-out infinite alternate',
              zIndex: -1
            },
            '@keyframes pulseGlow': {
              '0%': { opacity: 0.5, transform: 'scale(0.95)' },
              '100%': { opacity: 1, transform: 'scale(1.05)' }
            },
            '& .MuiPaper-root': {
              borderRadius: '12px',
              overflow: 'hidden',
              transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
              backdropFilter: 'blur(10px)',
              backgroundColor: 'transparent',
              // Dark navy blue gradient for cards
              background: 'linear-gradient(135deg, rgba(15, 35, 70, 0.7), rgba(25, 55, 100, 0.5))',
              border: '1px solid rgba(41, 121, 255, 0.1)',
              outline: 'none',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
              // Ensure no white borders on any elements
              '& *': {
                border: 'none',
                borderColor: 'transparent'
              },
              // Add subtle glow on hover
              '&:hover': {
                transform: 'translateY(-5px)',
                boxShadow: '0 8px 30px rgba(41, 121, 255, 0.15)',
                borderColor: 'rgba(41, 121, 255, 0.3)',
                background: 'linear-gradient(135deg, rgba(20, 40, 80, 0.8), rgba(30, 60, 110, 0.6))'
              },
              // Add subtle pulsing border effect
              '&::after': {
                content: '""',
                position: 'absolute',
                inset: 0,
                borderRadius: '12px',
                padding: '1px',
                background: 'linear-gradient(135deg, rgba(41, 121, 255, 0.5), rgba(25, 65, 125, 0.3))',
                WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
                WebkitMaskComposite: 'xor',
                maskComposite: 'exclude',
                opacity: 0,
                transition: 'opacity 0.3s ease',
              },
              '&:hover::after': {
                opacity: 1
              }
            },
            '& .MuiTableContainer-root': {
              background: 'transparent',
              backdropFilter: 'blur(10px)',
              border: 'none',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)',
              borderRadius: '12px',
              overflow: 'hidden'
            },
            '& .MuiTable-root': {
              background: 'transparent',
              border: 'none'
            },
            '& .MuiTableHead-root': {
              background: 'linear-gradient(135deg, rgba(15, 35, 70, 0.9), rgba(25, 55, 100, 0.7))',
              border: 'none'
            },
            '& .MuiTableRow-root': {
              transition: 'all 0.3s ease',
              border: 'none',
              '&:hover': {
                background: 'rgba(41, 121, 255, 0.1)',
                transform: 'translateX(5px)'
              }
            },
            // Style table cells
            '& .MuiTableCell-root': {
              borderBottom: '1px solid rgba(41, 121, 255, 0.1)',
              borderTop: 'none',
              borderLeft: 'none',
              borderRight: 'none',
              color: 'rgba(255, 255, 255, 0.85)',
              padding: '16px',
              fontSize: '0.95rem'
            },
            // Add glowing effect to buttons
            '& .MuiButton-root': {
              position: 'relative',
              overflow: 'hidden',
              borderRadius: '8px',
              transition: 'all 0.3s ease',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: '-50%',
                left: '-50%',
                width: '200%',
                height: '200%',
                background: 'linear-gradient(45deg, rgba(41, 121, 255, 0) 0%, rgba(41, 121, 255, 0.1) 50%, rgba(41, 121, 255, 0) 100%)',
                transform: 'rotate(45deg)',
                transition: 'all 0.5s ease',
                opacity: 0
              },
              '&:hover::after': {
                opacity: 1,
                left: '100%',
                top: '100%',
                transition: 'all 0.5s ease'
              }
            }
          }}>
            <Outlet />
          </Box>
        </GlobalBackground>
      </Box>

      {/* Global Search Panel */}
      <AnimatePresence>
        {searchOpen && (
          <GlobalSearch onClose={toggleSearch} />
        )}
      </AnimatePresence>

      {/* Notifications Panel */}
      <AnimatePresence>
        {notificationsOpen && (
          <NotificationsPanel onClose={toggleNotifications} />
        )}
      </AnimatePresence>

      {/* Weather Widget */}
      <AnimatePresence>
        {weatherOpen && (
          <WeatherWidget onClose={toggleWeather} />
        )}
      </AnimatePresence>

      {/* Quick Actions */}
      <AnimatePresence>
        {quickActionsOpen && (
          <QuickActions onClose={toggleQuickActions} />
        )}
      </AnimatePresence>
    </Box>
    </>
  );
};

export default UnifiedDashboardLayout;
