import axios from 'axios';
import { Animal } from '../hooks/useAnimalData';

// MongoDB connection string
const MONGODB_URI = process.env.REACT_APP_MONGODB_URI || 'mongodb+srv://AMPD:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3002/api';

// Animal API endpoints
const ENDPOINTS = {
  ANIMALS: '/animals',
  ANIMAL_BY_ID: (id: string) => `/animals/${id}`,
  ANIMAL_STATS: '/animals/stats',
  HEALTH_RECORDS: '/health-records',
  FEEDING_RECORDS: '/feeding-records',
  BREEDING_RECORDS: '/breeding-records',
};

// MongoDB service for animals
export const mongoService = {
  // Get all animals
  getAnimals: async (): Promise<Animal[]> => {
    try {
      const response = await axios.get(`${API_BASE_URL}${ENDPOINTS.ANIMALS}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching animals:', error);
      throw error;
    }
  },

  // Get animal by ID
  getAnimalById: async (id: string): Promise<Animal> => {
    try {
      const response = await axios.get(`${API_BASE_URL}${ENDPOINTS.ANIMAL_BY_ID(id)}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching animal with ID ${id}:`, error);
      throw error;
    }
  },

  // Create new animal
  createAnimal: async (animal: Omit<Animal, 'id'>): Promise<Animal> => {
    try {
      const response = await axios.post(`${API_BASE_URL}${ENDPOINTS.ANIMALS}`, animal);
      return response.data;
    } catch (error) {
      console.error('Error creating animal:', error);
      throw error;
    }
  },

  // Update animal
  updateAnimal: async (id: string, animal: Partial<Animal>): Promise<Animal> => {
    try {
      const response = await axios.put(`${API_BASE_URL}${ENDPOINTS.ANIMAL_BY_ID(id)}`, animal);
      return response.data;
    } catch (error) {
      console.error(`Error updating animal with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete animal
  deleteAnimal: async (id: string): Promise<void> => {
    try {
      await axios.delete(`${API_BASE_URL}${ENDPOINTS.ANIMAL_BY_ID(id)}`);
    } catch (error) {
      console.error(`Error deleting animal with ID ${id}:`, error);
      throw error;
    }
  },

  // Get animal statistics
  getAnimalStats: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}${ENDPOINTS.ANIMAL_STATS}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching animal statistics:', error);
      throw error;
    }
  },

  // Get health records for an animal
  getHealthRecords: async (animalId: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}${ENDPOINTS.HEALTH_RECORDS}?animalId=${animalId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching health records for animal with ID ${animalId}:`, error);
      throw error;
    }
  },

  // Get feeding records for an animal
  getFeedingRecords: async (animalId: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}${ENDPOINTS.FEEDING_RECORDS}?animalId=${animalId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching feeding records for animal with ID ${animalId}:`, error);
      throw error;
    }
  },

  // Get breeding records for an animal
  getBreedingRecords: async (animalId: string) => {
    try {
      const response = await axios.get(`${API_BASE_URL}${ENDPOINTS.BREEDING_RECORDS}?animalId=${animalId}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching breeding records for animal with ID ${animalId}:`, error);
      throw error;
    }
  },
};

export default mongoService;
