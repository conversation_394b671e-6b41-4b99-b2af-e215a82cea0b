/* Sidebar.css - External styles for the Sidebar component */

.sidebar-container {
  height: 100vh;
  text-align: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  overflow: hidden;
  z-index: 20;
  border-right: none;
  box-shadow: none;
  margin-right: 0;
  margin-left: 0;
  padding-left: 0;
  left: 0;
  width: 100%;
}

.sidebar-background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.07;
  background-image: url(https://www.transparenttextures.com/patterns/cubes.png);
  z-index: 0;
}

.sidebar-background-image {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.05;
  background-size: cover;
  background-position: center;
  z-index: 0;
  filter: blur(1px);
  /* Background image is set dynamically in the component */
}

.sidebar-logo-section {
  padding: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  position: relative;
  z-index: 10;
  border: none;
  overflow: hidden;
  background: linear-gradient(to right, rgba(10, 30, 60, 0.95), rgba(20, 40, 80, 0.9));
  box-shadow: none;
}

.sidebar-navigation {
  flex: 1;
  overflow-y: auto;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  position: relative;
  z-index: 10;
  min-height: calc(100vh - 180px);
}

.sidebar-item-name {
  font-size: 1.5rem;
  letter-spacing: 0.01em;
  font-weight: 500;
  color: white;
}

.sidebar-item-description {
  font-size: 1.2rem;
  margin-top: 3px;
  display: block;
  opacity: 0.9;
}

.sidebar-user-section {
  padding: 1rem;
  position: relative;
  z-index: 10;
  border-top: none;
  overflow: hidden;
  background: linear-gradient(to right, rgba(10, 30, 60, 0.95), rgba(20, 40, 80, 0.9));
  box-shadow: none;
}

.sidebar-username {
  font-size: 1.1rem;
  font-weight: 500;
  color: white;
}

.sidebar-email {
  font-size: 0.95rem;
  opacity: 0.8;
}

.sidebar-toggle-button {
  padding: 0.375rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
  border: none;
  background: transparent;
  color: white;
}

.sidebar-toggle-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-subitem {
  display: block;
  padding: 0.75rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 1.2rem;
  font-weight: 500;
  transition: all 0.3s;
}

.sidebar-subitem-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  transform: translateX(0.25rem);
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar-subitem-inactive {
  color: rgba(255, 255, 255, 0.7);
}

.sidebar-subitem-inactive:hover {
  background-color: rgba(255, 255, 255, 0.15);
  color: white;
  transform: translateX(0.25rem);
  border: none;
}
