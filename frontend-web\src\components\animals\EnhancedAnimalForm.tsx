import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Button,
  Alert
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import dayjs from 'dayjs';

interface EnhancedAnimalFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  initialData?: any;
  mode: 'create' | 'edit';
}

const EnhancedAnimalForm: React.FC<EnhancedAnimalFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialData,
  mode
}) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form state with comprehensive fields
  const [formData, setFormData] = useState({
    // Basic Information
    tagNumber: '',
    name: '',
    species: '',
    breed: '',
    gender: '',
    birthDate: null as dayjs.Dayjs | null,
    
    // Physical Characteristics
    weight: '',
    height: '',
    length: '',
    chestGirth: '',
    primaryColor: '',
    secondaryColor: '',
    markings: [] as string[],
    bodyConditionScore: '',
    
    // Identification
    rfidTag: '',
    microchipId: '',
    earTagLeft: '',
    earTagRight: '',
    tattooId: '',
    brandMark: '',
    registrationNumber: '',
    passportNumber: '',
    nationalId: '',
    
    // Genealogy
    sireId: '',
    sireTagNumber: '',
    sireName: '',
    sireBreed: '',
    sireLineage: '',
    damId: '',
    damTagNumber: '',
    damName: '',
    damBreed: '',
    damLineage: '',
    bloodline: '',
    geneticMarkers: [] as string[],
    
    // Acquisition Details
    purchasePrice: '',
    purchaseDate: null as dayjs.Dayjs | null,
    vendorName: '',
    vendorContact: '',
    vendorAddress: '',
    transportMethod: '',
    transportCost: '',
    quarantineRequired: false,
    quarantineStartDate: null as dayjs.Dayjs | null,
    quarantineEndDate: null as dayjs.Dayjs | null,
    
    // Health and Status
    healthStatus: 'healthy',
    location: '',
    notes: ''
  });

  // Species and breed options
  const speciesOptions = [
    { value: 'cattle', label: 'Cattle' },
    { value: 'sheep', label: 'Sheep' },
    { value: 'goat', label: 'Goat' },
    { value: 'pig', label: 'Pig' },
    { value: 'chicken', label: 'Chicken' },
    { value: 'horse', label: 'Horse' },
    { value: 'other', label: 'Other' }
  ];

  const breedOptions = {
    cattle: ['Nguni', 'Brahman', 'Angus', 'Hereford', 'Simmental', 'Charolais', 'Limousin'],
    sheep: ['Dorper', 'Merino', 'Blackhead Persian', 'Damara', 'Boer Goat Cross'],
    goat: ['Boer', 'Kalahari Red', 'Savanna', 'Angora', 'Indigenous'],
    pig: ['Large White', 'Landrace', 'Duroc', 'Hampshire', 'Pietrain'],
    chicken: ['Broiler', 'Layer', 'Indigenous', 'Potchefstroom Koekoek'],
    horse: ['Thoroughbred', 'Arabian', 'Quarter Horse', 'Friesian', 'Boerperd']
  };

  const colorOptions = [
    'Black', 'White', 'Brown', 'Red', 'Grey', 'Cream', 'Spotted', 'Brindle', 'Roan'
  ];

  const markingOptions = [
    'White face', 'White legs', 'White belly', 'Star on forehead', 'Stripe on face',
    'Spotted pattern', 'Solid color', 'Mixed colors', 'Distinctive markings'
  ];

  // Initialize form data
  useEffect(() => {
    if (initialData && mode === 'edit') {
      setFormData({
        ...formData,
        ...initialData,
        birthDate: initialData.birthDate ? dayjs(initialData.birthDate) : null,
        purchaseDate: initialData.purchaseDate ? dayjs(initialData.purchaseDate) : null,
        quarantineStartDate: initialData.quarantineStartDate ? dayjs(initialData.quarantineStartDate) : null,
        quarantineEndDate: initialData.quarantineEndDate ? dayjs(initialData.quarantineEndDate) : null
      });
    }
  }, [initialData, mode]);

  const handleInputChange = (field: string) => (event: any) => {
    const value = event.target ? event.target.value : event;
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateChange = (field: string) => (date: dayjs.Dayjs | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: date
    }));
  };

  const handleArrayChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field as keyof typeof prev].includes(value)
        ? (prev[field as keyof typeof prev] as string[]).filter(item => item !== value)
        : [...(prev[field as keyof typeof prev] as string[]), value]
    }));
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // Prepare data for submission
      const submitData = {
        ...formData,
        birthDate: formData.birthDate?.toISOString(),
        purchaseDate: formData.purchaseDate?.toISOString(),
        quarantineStartDate: formData.quarantineStartDate?.toISOString(),
        quarantineEndDate: formData.quarantineEndDate?.toISOString(),
        weight: formData.weight ? parseFloat(formData.weight) : undefined,
        height: formData.height ? parseFloat(formData.height) : undefined,
        length: formData.length ? parseFloat(formData.length) : undefined,
        chestGirth: formData.chestGirth ? parseFloat(formData.chestGirth) : undefined,
        purchasePrice: formData.purchasePrice ? parseFloat(formData.purchasePrice) : undefined,
        transportCost: formData.transportCost ? parseFloat(formData.transportCost) : undefined,
        bodyConditionScore: formData.bodyConditionScore ? parseInt(formData.bodyConditionScore) : undefined
      };

      await onSubmit(submitData);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save animal');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { 
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h5" component="div">
          {mode === 'create' ? 'Add New Animal' : 'Edit Animal'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Complete animal registration with genealogy and lifecycle tracking
        </Typography>
      </DialogTitle>
      
      <DialogContent sx={{ pb: 1 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Basic Information Section */}
        <Accordion defaultExpanded>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Basic Information</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Tag Number *"
                  value={formData.tagNumber}
                  onChange={handleInputChange('tagNumber')}
                  placeholder="e.g., CTL-2025-001"
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Animal Name"
                  value={formData.name}
                  onChange={handleInputChange('name')}
                  placeholder="Optional name"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel>Species</InputLabel>
                  <Select
                    value={formData.species}
                    onChange={handleInputChange('species')}
                    label="Species"
                  >
                    {speciesOptions.map(option => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Breed</InputLabel>
                  <Select
                    value={formData.breed}
                    onChange={handleInputChange('breed')}
                    label="Breed"
                    disabled={!formData.species}
                  >
                    {formData.species && breedOptions[formData.species as keyof typeof breedOptions]?.map(breed => (
                      <MenuItem key={breed} value={breed}>
                        {breed}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required>
                  <InputLabel>Gender</InputLabel>
                  <Select
                    value={formData.gender}
                    onChange={handleInputChange('gender')}
                    label="Gender"
                  >
                    <MenuItem value="male">Male</MenuItem>
                    <MenuItem value="female">Female</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <DatePicker
                  label="Birth Date"
                  value={formData.birthDate}
                  onChange={handleDateChange('birthDate')}
                  slotProps={{
                    textField: {
                      fullWidth: true
                    }
                  }}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Physical Characteristics Section */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Physical Characteristics</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Weight (kg)"
                  type="number"
                  value={formData.weight}
                  onChange={handleInputChange('weight')}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Height (cm)"
                  type="number"
                  value={formData.height}
                  onChange={handleInputChange('height')}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Length (cm)"
                  type="number"
                  value={formData.length}
                  onChange={handleInputChange('length')}
                />
              </Grid>
              <Grid item xs={12} sm={3}>
                <TextField
                  fullWidth
                  label="Chest Girth (cm)"
                  type="number"
                  value={formData.chestGirth}
                  onChange={handleInputChange('chestGirth')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Primary Color</InputLabel>
                  <Select
                    value={formData.primaryColor}
                    onChange={handleInputChange('primaryColor')}
                    label="Primary Color"
                  >
                    {colorOptions.map(color => (
                      <MenuItem key={color} value={color}>
                        {color}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Secondary Color</InputLabel>
                  <Select
                    value={formData.secondaryColor}
                    onChange={handleInputChange('secondaryColor')}
                    label="Secondary Color"
                  >
                    {colorOptions.map(color => (
                      <MenuItem key={color} value={color}>
                        {color}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Markings and Features
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {markingOptions.map(marking => (
                    <Chip
                      key={marking}
                      label={marking}
                      onClick={() => handleArrayChange('markings', marking)}
                      color={formData.markings.includes(marking) ? 'primary' : 'default'}
                      variant={formData.markings.includes(marking) ? 'filled' : 'outlined'}
                    />
                  ))}
                </Box>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Body Condition Score (1-9)"
                  type="number"
                  value={formData.bodyConditionScore}
                  onChange={handleInputChange('bodyConditionScore')}
                  inputProps={{ min: 1, max: 9 }}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Identification Section */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Identification & Documentation</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="RFID Tag"
                  value={formData.rfidTag}
                  onChange={handleInputChange('rfidTag')}
                  placeholder="e.g., 982000123456789"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Microchip ID"
                  value={formData.microchipId}
                  onChange={handleInputChange('microchipId')}
                  placeholder="e.g., 123456789012345"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Left Ear Tag"
                  value={formData.earTagLeft}
                  onChange={handleInputChange('earTagLeft')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Right Ear Tag"
                  value={formData.earTagRight}
                  onChange={handleInputChange('earTagRight')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Tattoo ID"
                  value={formData.tattooId}
                  onChange={handleInputChange('tattooId')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Brand Mark"
                  value={formData.brandMark}
                  onChange={handleInputChange('brandMark')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Registration Number"
                  value={formData.registrationNumber}
                  onChange={handleInputChange('registrationNumber')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Passport Number"
                  value={formData.passportNumber}
                  onChange={handleInputChange('passportNumber')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="National ID"
                  value={formData.nationalId}
                  onChange={handleInputChange('nationalId')}
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Genealogy Section */}
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">Genealogy & Breeding Information</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold' }}>
                  Sire (Father) Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Sire Tag Number"
                  value={formData.sireTagNumber}
                  onChange={handleInputChange('sireTagNumber')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Sire Name"
                  value={formData.sireName}
                  onChange={handleInputChange('sireName')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Sire Breed"
                  value={formData.sireBreed}
                  onChange={handleInputChange('sireBreed')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Sire Lineage"
                  value={formData.sireLineage}
                  onChange={handleInputChange('sireLineage')}
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold', mt: 2 }}>
                  Dam (Mother) Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Dam Tag Number"
                  value={formData.damTagNumber}
                  onChange={handleInputChange('damTagNumber')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Dam Name"
                  value={formData.damName}
                  onChange={handleInputChange('damName')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Dam Breed"
                  value={formData.damBreed}
                  onChange={handleInputChange('damBreed')}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Dam Lineage"
                  value={formData.damLineage}
                  onChange={handleInputChange('damLineage')}
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 'bold', mt: 2 }}>
                  Genetic Information
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Bloodline"
                  value={formData.bloodline}
                  onChange={handleInputChange('bloodline')}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Genetic Markers (comma-separated)"
                  value={formData.geneticMarkers.join(', ')}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    geneticMarkers: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                  }))}
                  placeholder="e.g., A2A2, BB, Polled"
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>
      </DialogContent>

      <DialogActions sx={{ p: 3 }}>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !formData.tagNumber || !formData.species || !formData.gender}
        >
          {loading ? 'Saving...' : mode === 'create' ? 'Add Animal' : 'Update Animal'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EnhancedAnimalForm;
