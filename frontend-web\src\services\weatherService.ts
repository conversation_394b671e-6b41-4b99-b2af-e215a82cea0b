/**
 * Weather Service for AgriIntel
 * Integrates with weather APIs to provide real-time weather data and alerts
 * Captures farmer location during registration and provides location-based weather
 */

import axios from 'axios';

// Weather API configuration
const WEATHER_API_KEY = process.env.REACT_APP_WEATHER_API_KEY || 'demo_key';
const WEATHER_BASE_URL = 'https://api.openweathermap.org/data/2.5';

// South African farming regions for fallback
const SA_FARMING_REGIONS = [
  { name: 'Johannesburg', lat: -26.2041, lon: 28.0473, province: 'Gauteng' },
  { name: 'Cape Town', lat: -33.9249, lon: 18.4241, province: 'Western Cape' },
  { name: 'Durban', lat: -29.8587, lon: 31.0218, province: 'KwaZulu-Natal' },
  { name: 'Pretoria', lat: -25.7479, lon: 28.2293, province: 'Gauteng' },
  { name: 'Bloemfontein', lat: -29.0852, lon: 26.1596, province: 'Free State' },
  { name: 'Polokwane', lat: -23.9045, lon: 29.4689, province: 'Limpopo' }
];

interface WeatherData {
  current: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    windDirection: string;
    pressure: number;
    visibility: number;
    uvIndex: number;
    condition: string;
    icon: string;
    feelsLike: number;
  };
  forecast: WeatherForecast[];
  alerts: WeatherAlert[];
  location: {
    name: string;
    region: string;
    country: string;
    lat: number;
    lon: number;
  };
  lastUpdated: string;
}

interface WeatherForecast {
  date: string;
  maxTemp: number;
  minTemp: number;
  condition: string;
  icon: string;
  humidity: number;
  chanceOfRain: number;
  windSpeed: number;
  sunrise: string;
  sunset: string;
}

interface WeatherAlert {
  id: string;
  title: string;
  description: string;
  severity: 'minor' | 'moderate' | 'severe' | 'extreme';
  urgency: 'immediate' | 'expected' | 'future';
  areas: string[];
  effective: string;
  expires: string;
  instruction?: string;
}

interface FarmingAdvice {
  category: 'irrigation' | 'planting' | 'harvesting' | 'livestock' | 'general';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  actionRequired: boolean;
  timeframe: string;
}

class WeatherService {
  private apiKey: string;
  private baseUrl: string;
  private cache: Map<string, { data: any; timestamp: number }> = new Map();
  private cacheTimeout = 10 * 60 * 1000; // 10 minutes
  private userLocation: { lat: number; lon: number; city: string; province: string } | null = null;

  constructor() {
    this.apiKey = process.env.REACT_APP_WEATHER_API_KEY || 'demo_key';
    this.baseUrl = 'https://api.weatherapi.com/v1';
    this.initializeLocation();
  }

  /**
   * Initialize user location on service creation
   */
  private async initializeLocation(): Promise<void> {
    try {
      await this.getCurrentLocation();
    } catch (error) {
      console.warn('Could not initialize location:', error);
      // Set default to Johannesburg
      this.userLocation = {
        lat: -26.2041,
        lon: 28.0473,
        city: 'Johannesburg',
        province: 'Gauteng'
      };
    }
  }

  /**
   * Get user's current location using browser geolocation
   */
  async getCurrentLocation(): Promise<{ lat: number; lon: number; city: string; province: string }> {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        const fallback = SA_FARMING_REGIONS[0];
        this.userLocation = {
          lat: fallback.lat,
          lon: fallback.lon,
          city: fallback.name,
          province: fallback.province
        };
        resolve(this.userLocation);
        return;
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          const nearest = this.findNearestFarmingRegion(latitude, longitude);
          this.userLocation = {
            lat: latitude,
            lon: longitude,
            city: nearest.name,
            province: nearest.province
          };
          resolve(this.userLocation);
        },
        (error) => {
          console.warn('Geolocation error:', error);
          const fallback = SA_FARMING_REGIONS[0];
          this.userLocation = {
            lat: fallback.lat,
            lon: fallback.lon,
            city: fallback.name,
            province: fallback.province
          };
          resolve(this.userLocation);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5 minutes
        }
      );
    });
  }

  /**
   * Find nearest farming region for location context
   */
  private findNearestFarmingRegion(lat: number, lon: number) {
    let nearest = SA_FARMING_REGIONS[0];
    let minDistance = this.calculateDistance(lat, lon, nearest.lat, nearest.lon);

    for (const region of SA_FARMING_REGIONS) {
      const distance = this.calculateDistance(lat, lon, region.lat, region.lon);
      if (distance < minDistance) {
        minDistance = distance;
        nearest = region;
      }
    }

    return nearest;
  }

  /**
   * Calculate distance between two coordinates
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Set user location manually (for registration)
   */
  setUserLocation(lat: number, lon: number, city?: string, province?: string): void {
    const nearest = this.findNearestFarmingRegion(lat, lon);
    this.userLocation = {
      lat,
      lon,
      city: city || nearest.name,
      province: province || nearest.province
    };
  }

  /**
   * Get stored user location
   */
  getUserLocation() {
    return this.userLocation;
  }

  /**
   * Get weather for user's current location
   */
  async getCurrentLocationWeather(): Promise<WeatherData> {
    if (!this.userLocation) {
      await this.getCurrentLocation();
    }

    const location = `${this.userLocation!.lat},${this.userLocation!.lon}`;
    return this.getWeatherData(location);
  }

  /**
   * Get current weather and forecast for a location
   */
  async getWeatherData(location: string): Promise<WeatherData> {
    try {
      const cacheKey = `weather_${location}`;
      const cached = this.cache.get(cacheKey);
      
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }

      const response = await fetch(
        `${this.baseUrl}/forecast.json?key=${this.apiKey}&q=${encodeURIComponent(location)}&days=7&aqi=yes&alerts=yes`
      );

      if (!response.ok) {
        throw new Error(`Weather API error: ${response.status}`);
      }

      const data = await response.json();
      const weatherData = this.transformWeatherData(data);
      
      // Cache the result
      this.cache.set(cacheKey, { data: weatherData, timestamp: Date.now() });
      
      return weatherData;
    } catch (error) {
      console.error('Failed to fetch weather data:', error);
      return this.getMockWeatherData(location);
    }
  }

  /**
   * Get weather data by coordinates
   */
  async getWeatherByCoordinates(lat: number, lon: number): Promise<WeatherData> {
    return this.getWeatherData(`${lat},${lon}`);
  }

  /**
   * Get farming advice based on current weather conditions
   */
  async getFarmingAdvice(location: string): Promise<FarmingAdvice[]> {
    try {
      const weatherData = await this.getWeatherData(location);
      return this.generateFarmingAdvice(weatherData);
    } catch (error) {
      console.error('Failed to get farming advice:', error);
      return [];
    }
  }

  /**
   * Get weather alerts for a location
   */
  async getWeatherAlerts(location: string): Promise<WeatherAlert[]> {
    try {
      const weatherData = await this.getWeatherData(location);
      return weatherData.alerts;
    } catch (error) {
      console.error('Failed to get weather alerts:', error);
      return [];
    }
  }

  /**
   * Transform API response to our weather data format
   */
  private transformWeatherData(apiData: any): WeatherData {
    return {
      current: {
        temperature: apiData.current.temp_c,
        humidity: apiData.current.humidity,
        windSpeed: apiData.current.wind_kph,
        windDirection: apiData.current.wind_dir,
        pressure: apiData.current.pressure_mb,
        visibility: apiData.current.vis_km,
        uvIndex: apiData.current.uv,
        condition: apiData.current.condition.text,
        icon: apiData.current.condition.icon,
        feelsLike: apiData.current.feelslike_c
      },
      forecast: apiData.forecast.forecastday.map((day: any) => ({
        date: day.date,
        maxTemp: day.day.maxtemp_c,
        minTemp: day.day.mintemp_c,
        condition: day.day.condition.text,
        icon: day.day.condition.icon,
        humidity: day.day.avghumidity,
        chanceOfRain: day.day.daily_chance_of_rain,
        windSpeed: day.day.maxwind_kph,
        sunrise: day.astro.sunrise,
        sunset: day.astro.sunset
      })),
      alerts: (apiData.alerts?.alert || []).map((alert: any) => ({
        id: alert.id || Date.now().toString(),
        title: alert.headline,
        description: alert.desc,
        severity: this.mapSeverity(alert.severity),
        urgency: this.mapUrgency(alert.urgency),
        areas: alert.areas ? alert.areas.split(';') : [],
        effective: alert.effective,
        expires: alert.expires,
        instruction: alert.instruction
      })),
      location: {
        name: apiData.location.name,
        region: apiData.location.region,
        country: apiData.location.country,
        lat: apiData.location.lat,
        lon: apiData.location.lon
      },
      lastUpdated: apiData.current.last_updated
    };
  }

  /**
   * Generate farming advice based on weather conditions
   */
  private generateFarmingAdvice(weatherData: WeatherData): FarmingAdvice[] {
    const advice: FarmingAdvice[] = [];
    const current = weatherData.current;
    const forecast = weatherData.forecast[0];

    // Temperature-based advice
    if (current.temperature > 35) {
      advice.push({
        category: 'livestock',
        title: 'High Temperature Alert',
        description: 'Provide extra shade and water for livestock. Consider adjusting feeding times to cooler parts of the day.',
        priority: 'high',
        actionRequired: true,
        timeframe: 'immediate'
      });
    }

    if (current.temperature < 5) {
      advice.push({
        category: 'livestock',
        title: 'Cold Weather Protection',
        description: 'Ensure livestock have adequate shelter and bedding. Check water sources for freezing.',
        priority: 'high',
        actionRequired: true,
        timeframe: 'immediate'
      });
    }

    // Rain and irrigation advice
    if (forecast.chanceOfRain > 70) {
      advice.push({
        category: 'irrigation',
        title: 'Rain Expected',
        description: 'Heavy rain is expected. Consider postponing irrigation and ensure proper drainage.',
        priority: 'medium',
        actionRequired: true,
        timeframe: 'today'
      });
    } else if (forecast.chanceOfRain < 20 && current.humidity < 40) {
      advice.push({
        category: 'irrigation',
        title: 'Dry Conditions',
        description: 'Low humidity and no rain expected. Increase irrigation frequency for crops.',
        priority: 'medium',
        actionRequired: true,
        timeframe: 'next 24 hours'
      });
    }

    // Wind advice
    if (current.windSpeed > 50) {
      advice.push({
        category: 'general',
        title: 'High Wind Warning',
        description: 'Strong winds detected. Secure loose equipment and check fencing. Avoid spraying operations.',
        priority: 'high',
        actionRequired: true,
        timeframe: 'immediate'
      });
    }

    // UV advice
    if (current.uvIndex > 8) {
      advice.push({
        category: 'livestock',
        title: 'High UV Index',
        description: 'Very high UV levels. Ensure livestock have adequate shade and consider sun protection measures.',
        priority: 'medium',
        actionRequired: true,
        timeframe: 'during daylight hours'
      });
    }

    return advice;
  }

  /**
   * Get mock weather data for demo purposes
   */
  private getMockWeatherData(location: string): WeatherData {
    return {
      current: {
        temperature: 22,
        humidity: 65,
        windSpeed: 15,
        windDirection: 'SW',
        pressure: 1013,
        visibility: 10,
        uvIndex: 6,
        condition: 'Partly cloudy',
        icon: '//cdn.weatherapi.com/weather/64x64/day/116.png',
        feelsLike: 24
      },
      forecast: [
        {
          date: new Date().toISOString().split('T')[0],
          maxTemp: 26,
          minTemp: 18,
          condition: 'Partly cloudy',
          icon: '//cdn.weatherapi.com/weather/64x64/day/116.png',
          humidity: 60,
          chanceOfRain: 20,
          windSpeed: 18,
          sunrise: '06:30',
          sunset: '18:45'
        }
      ],
      alerts: [],
      location: {
        name: location,
        region: 'Demo Region',
        country: 'South Africa',
        lat: -26.2041,
        lon: 28.0473
      },
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Map API severity to our format
   */
  private mapSeverity(severity: string): 'minor' | 'moderate' | 'severe' | 'extreme' {
    switch (severity?.toLowerCase()) {
      case 'minor': return 'minor';
      case 'moderate': return 'moderate';
      case 'severe': return 'severe';
      case 'extreme': return 'extreme';
      default: return 'moderate';
    }
  }

  /**
   * Map API urgency to our format
   */
  private mapUrgency(urgency: string): 'immediate' | 'expected' | 'future' {
    switch (urgency?.toLowerCase()) {
      case 'immediate': return 'immediate';
      case 'expected': return 'expected';
      case 'future': return 'future';
      default: return 'expected';
    }
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache status
   */
  getCacheStatus(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Create singleton instance
const weatherService = new WeatherService();

export { weatherService };
export default weatherService;
export type { WeatherData, WeatherForecast, WeatherAlert, FarmingAdvice };
